<template>
  <div class="keyboard-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-group">
        <button @click="undo" :disabled="!canUndo" title="撤销 (Ctrl+Z)">
          <i class="icon-undo"></i>
        </button>
        <button @click="redo" :disabled="!canRedo" title="重做 (Ctrl+Y)">
          <i class="icon-redo"></i>
        </button>
      </div>
      
      <div class="toolbar-group">
        <button @click="addStandardKey" title="添加按键">
          <i class="icon-plus"></i> 添加按键
        </button>
        <button @click="removeSelectedKeys" :disabled="selectedKeys.length === 0" title="删除选中">
          <i class="icon-trash"></i> 删除
        </button>
      </div>
      
      <div class="toolbar-group">
        <button @click="exportJson" title="导出JSON">
          <i class="icon-download"></i> 导出
        </button>
        <input
          ref="fileInput"
          type="file"
          accept=".json"
          @change="importJson"
          style="display: none"
        />
        <button @click="$refs.fileInput.click()" title="导入JSON">
          <i class="icon-upload"></i> 导入
        </button>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 属性面板 -->
      <div class="property-panel">
        <div class="panel-section">
          <h3>键盘属性</h3>
          <div class="form-group">
            <label>名称:</label>
            <input v-model="meta.name" type="text" placeholder="键盘名称" />
          </div>
          <div class="form-group">
            <label>作者:</label>
            <input v-model="meta.author" type="text" placeholder="作者名称" />
          </div>
          <div class="form-group">
            <label>背景色:</label>
            <input v-model="meta.backcolor" type="color" />
          </div>
        </div>

        <div v-if="selectedKeys.length > 0" class="panel-section">
          <h3>按键属性</h3>
          <div class="form-group">
            <label>宽度:</label>
            <input
              v-model.number="multiKeyProps.width"
              type="number"
              step="0.25"
              min="0.25"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>高度:</label>
            <input
              v-model.number="multiKeyProps.height"
              type="number"
              step="0.25"
              min="0.25"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>按键颜色:</label>
            <input
              v-model="multiKeyProps.color"
              type="color"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>文字颜色:</label>
            <input
              v-model="multiKeyProps.textColor"
              type="color"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>标签:</label>
            <textarea
              v-model="multiKeyProps.labels"
              rows="3"
              placeholder="按键标签，每行一个"
              @input="updateSelectedKeyLabels"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- 键盘画布 -->
      <div class="keyboard-canvas-container">
        <KeyboardCanvas
          :keys="keys"
          :meta="meta"
          :selected-keys="selectedKeys"
          :hovered-key="hoveredKey"
          @key-click="handleKeyClick"
          @key-hover="handleKeyHover"
          @canvas-click="handleCanvasClick"
          @key-drag="handleKeyDrag"
        />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <span>按键数量: {{ keys.length }}</span>
      <span v-if="selectedKeys.length > 0">已选择: {{ selectedKeys.length }}</span>
      <span v-if="!saved" class="unsaved">未保存</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useKeyboard } from '@/composables/useKeyboard'
import { useFileOperations } from '@/composables/useFileOperations'
import KeyboardCanvas from './KeyboardCanvas.vue'

// 组合式函数
const {
  keys,
  selectedKeys,
  meta,
  canUndo,
  canRedo,
  addKey,
  removeSelectedKeys,
  updateSelectedKeys,
  selectKey,
  unselectAll,
  undo,
  redo
} = useKeyboard()

const { exportJson, importJson: importJsonFile } = useFileOperations()

// 响应式数据
const fileInput = ref(null)
const hoveredKey = ref(null)
const saved = ref(true)

// 多选按键的属性
const multiKeyProps = ref({
  width: 1,
  height: 1,
  color: '#cccccc',
  textColor: '#000000',
  labels: ''
})

// 计算属性
const hasSelection = computed(() => selectedKeys.value.length > 0)

// 监听选中按键变化，更新属性面板
watch(selectedKeys, (newSelection) => {
  if (newSelection.length === 1) {
    const key = newSelection[0]
    multiKeyProps.value = {
      width: key.width || 1,
      height: key.height || 1,
      color: key.color || '#cccccc',
      textColor: key.textColor || '#000000',
      labels: (key.labels || ['']).join('\n')
    }
  } else if (newSelection.length > 1) {
    // 多选时显示共同属性
    const firstKey = newSelection[0]
    multiKeyProps.value = {
      width: firstKey.width || 1,
      height: firstKey.height || 1,
      color: firstKey.color || '#cccccc',
      textColor: firstKey.textColor || '#000000',
      labels: ''
    }
  }
}, { immediate: true })

// 方法
const addStandardKey = () => {
  const newKeyData = {
    x: 0,
    y: 0,
    width: 1,
    height: 1,
    labels: [''],
    color: '#cccccc',
    textColor: '#000000'
  }
  
  // 如果有选中的按键，在其旁边添加
  if (selectedKeys.value.length > 0) {
    const lastSelected = selectedKeys.value[selectedKeys.value.length - 1]
    newKeyData.x = lastSelected.x + (lastSelected.width || 1)
    newKeyData.y = lastSelected.y
    newKeyData.color = lastSelected.color
    newKeyData.textColor = lastSelected.textColor
  }
  
  addKey(newKeyData)
}

const updateSelectedKeyProps = () => {
  if (selectedKeys.value.length > 0) {
    updateSelectedKeys({
      width: multiKeyProps.value.width,
      height: multiKeyProps.value.height,
      color: multiKeyProps.value.color,
      textColor: multiKeyProps.value.textColor
    })
  }
}

const updateSelectedKeyLabels = () => {
  if (selectedKeys.value.length === 1) {
    const labels = multiKeyProps.value.labels.split('\n').filter(label => label.trim() !== '')
    updateSelectedKeys({ labels })
  }
}

const handleKeyClick = (key, event) => {
  selectKey(key, {
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey
  })
}

const handleKeyHover = (key) => {
  hoveredKey.value = key
}

const handleCanvasClick = (event) => {
  if (!event.ctrlKey) {
    unselectAll()
  }
}

const handleKeyDrag = (key, deltaX, deltaY) => {
  if (selectedKeys.value.includes(key)) {
    // 拖拽所有选中的按键
    selectedKeys.value.forEach(selectedKey => {
      updateSelectedKeys({
        x: selectedKey.x + deltaX,
        y: selectedKey.y + deltaY
      })
    })
  } else {
    // 只拖拽当前按键
    updateSelectedKeys({
      x: key.x + deltaX,
      y: key.y + deltaY
    })
  }
}

const importJson = (event) => {
  const file = event.target.files[0]
  if (file) {
    importJsonFile(file)
    event.target.value = '' // 清空文件输入
  }
}

// 键盘快捷键
const handleKeyDown = (event) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'z':
        event.preventDefault()
        if (event.shiftKey) {
          redo()
        } else {
          undo()
        }
        break
      case 'y':
        event.preventDefault()
        redo()
        break
      case 'a':
        event.preventDefault()
        // 全选所有按键
        keys.value.forEach(key => selectKey(key, { ctrlKey: true }))
        break
      case 's':
        event.preventDefault()
        exportJson()
        break
    }
  } else if (event.key === 'Delete' || event.key === 'Backspace') {
    if (selectedKeys.value.length > 0) {
      event.preventDefault()
      removeSelectedKeys()
    }
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.keyboard-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: white;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-group {
  display: flex;
  gap: 0.5rem;
}

.toolbar button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar button:hover:not(:disabled) {
  background: #f0f0f0;
  border-color: #999;
}

.toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.property-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #ddd;
  overflow-y: auto;
  padding: 1rem;
}

.panel-section {
  margin-bottom: 2rem;
}

.panel-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.keyboard-canvas-container {
  flex: 1;
  overflow: auto;
  background: #fafafa;
}

.status-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: white;
  border-top: 1px solid #ddd;
  font-size: 0.9rem;
  color: #666;
}

.unsaved {
  color: #dc3545;
  font-weight: 500;
}

.icon-undo::before { content: "↶"; }
.icon-redo::before { content: "↷"; }
.icon-plus::before { content: "+"; }
.icon-trash::before { content: "🗑"; }
.icon-download::before { content: "↓"; }
.icon-upload::before { content: "↑"; }
</style>
