#!/bin/bash

# Keyboard Layout Editor - Production Build Script
# This script creates a production-ready build for deployment

set -e  # Exit on any error

echo "🚀 Building Keyboard Layout Editor for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Create build directory
BUILD_DIR="dist"
echo -e "${YELLOW}📁 Creating build directory: $BUILD_DIR${NC}"
rm -rf $BUILD_DIR
mkdir -p $BUILD_DIR

# Step 1: Install dependencies if needed
echo -e "${YELLOW}📦 Checking dependencies...${NC}"
if [ ! -d "bower_components" ]; then
    echo -e "${YELLOW}Installing bower dependencies...${NC}"
    make install
fi

# Step 2: Build all assets
echo -e "${YELLOW}🔨 Building assets...${NC}"
make all
make fonts

# Step 3: Copy essential files to build directory
echo -e "${YELLOW}📋 Copying files to build directory...${NC}"

# Main application files
cp kb.html $BUILD_DIR/
cp kb.js $BUILD_DIR/
cp render.js $BUILD_DIR/
cp serial.js $BUILD_DIR/
cp extensions.js $BUILD_DIR/
cp oauth.html $BUILD_DIR/
cp favicon.ico $BUILD_DIR/

# Configuration and data files
cp *.json $BUILD_DIR/
cp *.md $BUILD_DIR/

# Copy directories
echo -e "${YELLOW}📂 Copying asset directories...${NC}"
cp -r css $BUILD_DIR/
cp -r js $BUILD_DIR/
cp -r fonts $BUILD_DIR/
cp -r samples $BUILD_DIR/
cp -r bg $BUILD_DIR/

# Step 4: Create a simple index.html redirect
cat > $BUILD_DIR/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Keyboard Layout Editor</title>
    <meta http-equiv="refresh" content="0; url=kb.html">
    <link rel="canonical" href="kb.html">
</head>
<body>
    <p>Redirecting to <a href="kb.html">Keyboard Layout Editor</a>...</p>
</body>
</html>
EOF

# Step 5: Create deployment info
cat > $BUILD_DIR/build-info.json << EOF
{
    "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "buildVersion": "$(date +%Y%m%d-%H%M%S)",
    "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "gitBranch": "$(git branch --show-current 2>/dev/null || echo 'unknown')"
}
EOF

# Step 6: Create .htaccess for Apache servers (optional)
cat > $BUILD_DIR/.htaccess << 'EOF'
# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/ico "access plus 1 month"
    ExpiresByType application/ico "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# Default document
DirectoryIndex kb.html index.html
EOF

# Step 7: Show build summary
echo -e "${GREEN}✅ Production build completed!${NC}"
echo -e "${GREEN}📊 Build Summary:${NC}"
echo "   Build directory: $BUILD_DIR"
echo "   Total files: $(find $BUILD_DIR -type f | wc -l)"
echo "   Total size: $(du -sh $BUILD_DIR | cut -f1)"
echo ""
echo -e "${GREEN}📁 Files ready for deployment:${NC}"
ls -la $BUILD_DIR/

echo ""
echo -e "${YELLOW}🚀 Deployment Options:${NC}"
echo "1. Upload the entire '$BUILD_DIR' directory to your web server"
echo "2. Use rsync: rsync -av $BUILD_DIR/ user@server:/path/to/webroot/"
echo "3. Use SCP: scp -r $BUILD_DIR/* user@server:/path/to/webroot/"
echo "4. Use FTP client to upload all files in $BUILD_DIR"
echo ""
echo -e "${GREEN}🌐 Access your deployed app at: http://yourserver.com/kb.html${NC}"
