#!/bin/bash

# Keyboard Layout Editor - Vue.js 项目创建脚本
# 这个脚本将创建一个新的Vue.js版本的键盘布局编辑器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_NAME="vue-keyboard-layout-editor"
CURRENT_DIR=$(pwd)

echo -e "${BLUE}🚀 创建Vue.js版本的键盘布局编辑器${NC}"
echo ""

# 检查Node.js和npm
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm 未安装，请先安装 npm${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 版本: $(node --version)${NC}"
echo -e "${GREEN}✅ npm 版本: $(npm --version)${NC}"
echo ""

# 询问项目名称
read -p "项目名称 (默认: vue-keyboard-layout-editor): " input_name
if [ ! -z "$input_name" ]; then
    PROJECT_NAME="$input_name"
fi

# 检查目录是否存在
if [ -d "$PROJECT_NAME" ]; then
    echo -e "${YELLOW}⚠️  目录 '$PROJECT_NAME' 已存在${NC}"
    read -p "是否删除并重新创建? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        rm -rf "$PROJECT_NAME"
    else
        echo -e "${YELLOW}❌ 操作取消${NC}"
        exit 0
    fi
fi

echo -e "${YELLOW}📦 创建Vue项目...${NC}"

# 创建Vue项目
npm create vue@latest "$PROJECT_NAME" -- --typescript --router --pinia --eslint --prettier

cd "$PROJECT_NAME"

echo -e "${YELLOW}📦 安装额外依赖...${NC}"

# 安装项目特定的依赖
npm install \
    @vueuse/core \
    axios \
    file-saver \
    html2canvas \
    lodash-es \
    monaco-editor \
    sass

# 安装开发依赖
npm install -D \
    @types/file-saver \
    @types/lodash-es \
    vite-plugin-monaco-editor

echo -e "${YELLOW}📁 创建项目结构...${NC}"

# 创建目录结构
mkdir -p src/{components,composables,stores,utils,styles}
mkdir -p src/components/{keyboard,ui,editors}
mkdir -p src/styles/components
mkdir -p public/assets/{backgrounds,fonts,samples}

# 创建基础文件
cat > src/stores/keyboard.js << 'EOF'
import { defineStore } from 'pinia'

export const useKeyboardStore = defineStore('keyboard', {
  state: () => ({
    keys: [],
    meta: {
      backcolor: '#eeeeee',
      name: '',
      author: '',
      notes: '',
      background: { name: '', style: '' },
      radii: '',
      css: ''
    },
    selectedKeys: [],
    hoveredKey: null,
    history: [],
    currentHistoryIndex: -1,
    dirty: false,
    saved: true
  }),

  getters: {
    canUndo: (state) => state.currentHistoryIndex > 0,
    canRedo: (state) => state.currentHistoryIndex < state.history.length - 1,
    selectedKey: (state) => state.selectedKeys.length === 1 ? state.selectedKeys[0] : null,
    keyboardBounds: (state) => {
      if (state.keys.length === 0) return { width: 0, height: 0 }
      
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
      
      state.keys.forEach(key => {
        minX = Math.min(minX, key.x)
        minY = Math.min(minY, key.y)
        maxX = Math.max(maxX, key.x + (key.width || 1))
        maxY = Math.max(maxY, key.y + (key.height || 1))
      })
      
      return {
        width: maxX - minX,
        height: maxY - minY
      }
    }
  },

  actions: {
    addKey(keyData) {
      const newKey = {
        x: 0,
        y: 0,
        width: 1,
        height: 1,
        labels: [''],
        textColor: '#000000',
        color: '#cccccc',
        ...keyData,
        id: Date.now() + Math.random()
      }
      
      this.keys.push(newKey)
      this.markDirty()
    },

    removeKey(keyId) {
      const index = this.keys.findIndex(key => key.id === keyId)
      if (index !== -1) {
        this.keys.splice(index, 1)
        this.selectedKeys = this.selectedKeys.filter(key => key.id !== keyId)
        this.markDirty()
      }
    },

    updateKey(keyId, updates) {
      const key = this.keys.find(key => key.id === keyId)
      if (key) {
        Object.assign(key, updates)
        this.markDirty()
      }
    },

    selectKey(key, options = {}) {
      if (options.ctrlKey) {
        // 多选模式
        const index = this.selectedKeys.findIndex(k => k.id === key.id)
        if (index !== -1) {
          this.selectedKeys.splice(index, 1)
        } else {
          this.selectedKeys.push(key)
        }
      } else {
        // 单选模式
        this.selectedKeys = [key]
      }
    },

    unselectAll() {
      this.selectedKeys = []
    },

    markDirty() {
      this.dirty = true
      this.saved = false
    },

    saveSnapshot() {
      const snapshot = {
        keys: JSON.parse(JSON.stringify(this.keys)),
        meta: JSON.parse(JSON.stringify(this.meta)),
        timestamp: Date.now()
      }
      
      // 移除当前位置之后的历史记录
      this.history = this.history.slice(0, this.currentHistoryIndex + 1)
      this.history.push(snapshot)
      this.currentHistoryIndex = this.history.length - 1
      
      // 限制历史记录数量
      if (this.history.length > 50) {
        this.history.shift()
        this.currentHistoryIndex--
      }
    },

    undo() {
      if (this.canUndo) {
        this.currentHistoryIndex--
        const snapshot = this.history[this.currentHistoryIndex]
        this.keys = JSON.parse(JSON.stringify(snapshot.keys))
        this.meta = JSON.parse(JSON.stringify(snapshot.meta))
        this.selectedKeys = []
      }
    },

    redo() {
      if (this.canRedo) {
        this.currentHistoryIndex++
        const snapshot = this.history[this.currentHistoryIndex]
        this.keys = JSON.parse(JSON.stringify(snapshot.keys))
        this.meta = JSON.parse(JSON.stringify(snapshot.meta))
        this.selectedKeys = []
      }
    }
  }
})
EOF

cat > src/composables/useKeyboard.js << 'EOF'
import { computed } from 'vue'
import { useKeyboardStore } from '@/stores/keyboard'

export function useKeyboard() {
  const store = useKeyboardStore()

  const keys = computed(() => store.keys)
  const selectedKeys = computed(() => store.selectedKeys)
  const meta = computed(() => store.meta)
  const canUndo = computed(() => store.canUndo)
  const canRedo = computed(() => store.canRedo)

  const addKey = (keyData) => {
    store.saveSnapshot()
    store.addKey(keyData)
  }

  const removeSelectedKeys = () => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.removeKey(key.id)
      })
    }
  }

  const updateSelectedKeys = (updates) => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.updateKey(key.id, updates)
      })
    }
  }

  const selectKey = (key, options = {}) => {
    store.selectKey(key, options)
  }

  const unselectAll = () => {
    store.unselectAll()
  }

  const undo = () => {
    store.undo()
  }

  const redo = () => {
    store.redo()
  }

  return {
    keys,
    selectedKeys,
    meta,
    canUndo,
    canRedo,
    addKey,
    removeSelectedKeys,
    updateSelectedKeys,
    selectKey,
    unselectAll,
    undo,
    redo
  }
}
EOF

# 复制原项目的核心文件
echo -e "${YELLOW}📋 复制原项目核心文件...${NC}"

# 复制工具函数
cp "$CURRENT_DIR/serial.js" src/utils/ 2>/dev/null || echo "serial.js not found, skipping..."
cp "$CURRENT_DIR/render.js" src/utils/ 2>/dev/null || echo "render.js not found, skipping..."
cp "$CURRENT_DIR/js/color.js" src/utils/ 2>/dev/null || echo "color.js not found, skipping..."

# 复制样式文件
cp "$CURRENT_DIR/kb.css" src/styles/keyboard.scss 2>/dev/null || echo "kb.css not found, skipping..."

# 复制静态资源
cp -r "$CURRENT_DIR/bg/"* public/assets/backgrounds/ 2>/dev/null || echo "backgrounds not found, skipping..."
cp -r "$CURRENT_DIR/fonts/"* public/assets/fonts/ 2>/dev/null || echo "fonts not found, skipping..."
cp -r "$CURRENT_DIR/samples/"* public/assets/samples/ 2>/dev/null || echo "samples not found, skipping..."

# 复制配置文件
cp "$CURRENT_DIR"/*.json public/assets/ 2>/dev/null || echo "config files not found, skipping..."

echo -e "${YELLOW}⚙️  配置Vite...${NC}"

# 创建Vite配置
cat > vite.config.js << 'EOF'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 8080,
    host: true
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          editor: ['monaco-editor'],
          utils: ['lodash-es', 'axios']
        }
      }
    }
  }
})
EOF

# 更新package.json脚本
echo -e "${YELLOW}📝 更新package.json脚本...${NC}"

# 添加自定义脚本到package.json
npm pkg set scripts.build:legacy="npm run build && npm run build:legacy-support"
npm pkg set scripts.preview:host="npm run preview -- --host"
npm pkg set scripts.analyze="npx vite-bundle-analyzer"

echo ""
echo -e "${GREEN}✅ Vue.js项目创建完成！${NC}"
echo ""
echo -e "${BLUE}📁 项目位置: $(pwd)${NC}"
echo -e "${BLUE}🌐 开发服务器: http://localhost:8080${NC}"
echo ""
echo -e "${YELLOW}🚀 下一步操作:${NC}"
echo "1. cd $PROJECT_NAME"
echo "2. npm run dev"
echo ""
echo -e "${YELLOW}📋 可用命令:${NC}"
echo "  npm run dev          # 启动开发服务器"
echo "  npm run build        # 构建生产版本"
echo "  npm run preview      # 预览生产版本"
echo "  npm run lint         # 代码检查"
echo "  npm run format       # 代码格式化"
echo ""
echo -e "${GREEN}🎉 开始您的Vue.js键盘布局编辑器开发之旅吧！${NC}"
