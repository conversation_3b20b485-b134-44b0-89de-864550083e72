# Keyboard Layout Editor - Vue.js 迁移计划

## 🎯 项目改造目标

将基于AngularJS + Bower + Make的项目改造为现代化的Vue.js + npm/yarn + Vite项目。

## 📊 技术栈对比

| 组件 | 当前技术 | 目标技术 |
|------|----------|----------|
| 框架 | AngularJS 1.2.28 | Vue 3 + Composition API |
| 构建工具 | Make + Bower | Vite + npm/yarn |
| 包管理 | Bower | npm/yarn |
| CSS预处理 | Stylus | Sass/SCSS 或 PostCSS |
| 代码编辑器 | Ace Editor | Monaco Editor 或 CodeMirror |
| 状态管理 | $scope | Pinia |
| 路由 | $location | Vue Router |
| HTTP | $http | Axios |

## 🏗 项目结构设计

```
vue-keyboard-layout-editor/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── assets/
│       ├── backgrounds/     # 背景纹理
│       ├── fonts/          # 自定义字体
│       └── samples/        # 示例布局
├── src/
│   ├── main.js            # 应用入口
│   ├── App.vue            # 根组件
│   ├── components/        # Vue组件
│   │   ├── KeyboardEditor.vue    # 主编辑器
│   │   ├── KeyboardCanvas.vue    # 键盘画布
│   │   ├── KeyComponent.vue      # 单个按键
│   │   ├── PropertyPanel.vue     # 属性面板
│   │   ├── ToolBar.vue          # 工具栏
│   │   └── CodeEditor.vue       # 代码编辑器
│   ├── composables/       # 组合式函数
│   │   ├── useKeyboard.js       # 键盘状态管理
│   │   ├── useSelection.js      # 选择管理
│   │   ├── useUndoRedo.js       # 撤销重做
│   │   └── useFileOperations.js # 文件操作
│   ├── utils/             # 工具函数
│   │   ├── serial.js            # 数据序列化 (迁移)
│   │   ├── render.js            # 渲染逻辑 (迁移)
│   │   ├── color.js             # 颜色处理
│   │   └── keyboard-utils.js    # 键盘工具函数
│   ├── stores/            # Pinia状态管理
│   │   ├── keyboard.js          # 键盘数据状态
│   │   ├── ui.js               # UI状态
│   │   └── settings.js         # 设置状态
│   ├── styles/            # 样式文件
│   │   ├── main.scss           # 主样式
│   │   ├── keyboard.scss       # 键盘样式 (迁移)
│   │   └── components/         # 组件样式
│   └── assets/            # 静态资源
├── package.json
├── vite.config.js
├── tailwind.config.js     # 可选：Tailwind CSS
└── README.md
```

## 🔄 迁移步骤

### Phase 1: 项目初始化
1. 创建Vue 3项目脚手架
2. 配置Vite构建工具
3. 设置TypeScript支持 (可选)
4. 配置ESLint + Prettier

### Phase 2: 核心逻辑迁移
1. **数据层迁移**
   - 将 `serial.js` 改造为ES6模块
   - 将 `render.js` 改造为Vue组合式函数
   - 创建键盘数据类型定义

2. **状态管理**
   - 使用Pinia替代AngularJS的$scope
   - 创建keyboard store管理键盘数据
   - 创建selection store管理选择状态

### Phase 3: 组件化改造
1. **主编辑器组件** (`KeyboardEditor.vue`)
   - 整合原kb.js的控制器逻辑
   - 使用Composition API重构

2. **键盘画布组件** (`KeyboardCanvas.vue`)
   - 渲染键盘背景和按键
   - 处理鼠标交互和选择

3. **按键组件** (`KeyComponent.vue`)
   - 单个按键的渲染和交互
   - 支持拖拽、调整大小等

### Phase 4: UI组件迁移
1. **属性面板** - 替代原有的侧边栏
2. **工具栏** - 文件操作、撤销重做等
3. **代码编辑器** - 使用Monaco Editor替代Ace

### Phase 5: 功能完善
1. **文件操作** - 导入/导出JSON
2. **GitHub集成** - OAuth登录和Gist管理
3. **键盘预设** - 预定义布局和样式
4. **响应式设计** - 移动端适配

## 🛠 关键技术决策

### 1. 状态管理架构
```javascript
// stores/keyboard.js
import { defineStore } from 'pinia'

export const useKeyboardStore = defineStore('keyboard', {
  state: () => ({
    keys: [],
    meta: {},
    selectedKeys: [],
    history: [],
    currentHistoryIndex: -1
  }),
  
  actions: {
    addKey(keyData) { /* ... */ },
    removeKey(keyId) { /* ... */ },
    updateKey(keyId, updates) { /* ... */ },
    selectKey(keyId, multiSelect = false) { /* ... */ },
    undo() { /* ... */ },
    redo() { /* ... */ }
  }
})
```

### 2. 组合式函数设计
```javascript
// composables/useKeyboard.js
export function useKeyboard() {
  const store = useKeyboardStore()
  
  const addKey = (proto, nextline) => {
    // 迁移原addKey逻辑
  }
  
  const renderKey = (key) => {
    // 迁移原renderKey逻辑
  }
  
  return {
    keys: computed(() => store.keys),
    selectedKeys: computed(() => store.selectedKeys),
    addKey,
    renderKey,
    // ... 其他方法
  }
}
```

### 3. 组件通信模式
- 使用Props/Emits进行父子组件通信
- 使用Pinia进行跨组件状态共享
- 使用provide/inject处理深层组件通信

## 📦 依赖包选择

### 核心依赖
```json
{
  "dependencies": {
    "vue": "^3.3.0",
    "pinia": "^2.1.0",
    "vue-router": "^4.2.0",
    "@vueuse/core": "^10.0.0",
    "axios": "^1.4.0"
  },
  "devDependencies": {
    "vite": "^4.3.0",
    "@vitejs/plugin-vue": "^4.2.0",
    "sass": "^1.62.0",
    "eslint": "^8.42.0",
    "prettier": "^2.8.0"
  }
}
```

### 功能增强包
- **Monaco Editor**: 代码编辑器
- **html2canvas**: 截图功能
- **file-saver**: 文件下载
- **color**: 颜色处理
- **lodash-es**: 工具函数

## 🎨 样式迁移策略

1. **保持现有样式**: 将kb.css迁移为SCSS
2. **组件化样式**: 每个Vue组件包含自己的样式
3. **CSS变量**: 使用CSS自定义属性管理主题
4. **响应式设计**: 添加移动端支持

## 🧪 测试策略

1. **单元测试**: Vitest + Vue Test Utils
2. **E2E测试**: Playwright 或 Cypress
3. **组件测试**: Storybook (可选)

## 🚀 部署优化

1. **代码分割**: 使用Vite的动态导入
2. **资源优化**: 图片压缩、字体子集化
3. **PWA支持**: 离线使用能力
4. **CDN部署**: 静态资源CDN加速

## 📈 迁移收益

1. **开发体验**: 现代化开发工具链
2. **性能提升**: Vue 3的性能优势
3. **维护性**: 组件化架构，更好的代码组织
4. **扩展性**: 更容易添加新功能
5. **社区支持**: 活跃的Vue生态系统
