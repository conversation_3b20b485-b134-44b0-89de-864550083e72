import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export interface KeyData {
  id: string
  x: number
  y: number
  width: number
  height: number
  width2?: number
  height2?: number
  x2?: number
  y2?: number
  labels: string[]
  textColor: string
  color: string
  profile?: string
  nub?: boolean
  stepped?: boolean
  decal?: boolean
  rotation_angle?: number
  rotation_x?: number
  rotation_y?: number
  [key: string]: any
}

export interface KeyboardMeta {
  backcolor: string
  name: string
  author: string
  notes: string
  background: {
    name: string
    style: string
  }
  radii: string
  css: string
  [key: string]: any
}

export interface HistorySnapshot {
  keys: KeyData[]
  meta: KeyboardMeta
  timestamp: number
}

export const useKeyboardStore = defineStore('keyboard', () => {
  // 状态
  const keys = ref<KeyData[]>([])
  const meta = ref<KeyboardMeta>({
    backcolor: '#eeeeee',
    name: '',
    author: '',
    notes: '',
    background: { name: '', style: '' },
    radii: '',
    css: ''
  })
  
  const selectedKeys = ref<KeyData[]>([])
  const hoveredKey = ref<KeyData | null>(null)
  const history = ref<HistorySnapshot[]>([])
  const currentHistoryIndex = ref(-1)
  const dirty = ref(false)
  const saved = ref(true)

  // 计算属性
  const canUndo = computed(() => currentHistoryIndex.value > 0)
  const canRedo = computed(() => currentHistoryIndex.value < history.value.length - 1)
  const selectedKey = computed(() => selectedKeys.value.length === 1 ? selectedKeys.value[0] : null)
  
  const keyboardBounds = computed(() => {
    if (keys.value.length === 0) return { width: 0, height: 0 }
    
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
    
    keys.value.forEach(key => {
      minX = Math.min(minX, key.x)
      minY = Math.min(minY, key.y)
      maxX = Math.max(maxX, key.x + key.width)
      maxY = Math.max(maxY, key.y + key.height)
    })
    
    return {
      width: maxX - minX,
      height: maxY - minY
    }
  })

  // 动作
  const generateKeyId = () => `key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const addKey = (keyData: Partial<KeyData>) => {
    const newKey: KeyData = {
      id: generateKeyId(),
      x: 0,
      y: 0,
      width: 1,
      height: 1,
      labels: [''],
      textColor: '#000000',
      color: '#cccccc',
      ...keyData
    }
    
    keys.value.push(newKey)
    markDirty()
    return newKey
  }

  const removeKey = (keyId: string) => {
    const index = keys.value.findIndex(key => key.id === keyId)
    if (index !== -1) {
      keys.value.splice(index, 1)
      selectedKeys.value = selectedKeys.value.filter(key => key.id !== keyId)
      markDirty()
    }
  }

  const updateKey = (keyId: string, updates: Partial<KeyData>) => {
    const key = keys.value.find(key => key.id === keyId)
    if (key) {
      Object.assign(key, updates)
      markDirty()
    }
  }

  const selectKey = (key: KeyData, options: { ctrlKey?: boolean; shiftKey?: boolean; altKey?: boolean } = {}) => {
    if (options.ctrlKey) {
      // 多选模式
      const index = selectedKeys.value.findIndex(k => k.id === key.id)
      if (index !== -1) {
        selectedKeys.value.splice(index, 1)
      } else {
        selectedKeys.value.push(key)
      }
    } else {
      // 单选模式
      selectedKeys.value = [key]
    }
  }

  const unselectAll = () => {
    selectedKeys.value = []
  }

  const markDirty = () => {
    dirty.value = true
    saved.value = false
  }

  const saveSnapshot = () => {
    const snapshot: HistorySnapshot = {
      keys: JSON.parse(JSON.stringify(keys.value)),
      meta: JSON.parse(JSON.stringify(meta.value)),
      timestamp: Date.now()
    }
    
    // 移除当前位置之后的历史记录
    history.value = history.value.slice(0, currentHistoryIndex.value + 1)
    history.value.push(snapshot)
    currentHistoryIndex.value = history.value.length - 1
    
    // 限制历史记录数量
    if (history.value.length > 50) {
      history.value.shift()
      currentHistoryIndex.value--
    }
  }

  const undo = () => {
    if (canUndo.value) {
      currentHistoryIndex.value--
      const snapshot = history.value[currentHistoryIndex.value]
      keys.value = JSON.parse(JSON.stringify(snapshot.keys))
      meta.value = JSON.parse(JSON.stringify(snapshot.meta))
      selectedKeys.value = []
    }
  }

  const redo = () => {
    if (canRedo.value) {
      currentHistoryIndex.value++
      const snapshot = history.value[currentHistoryIndex.value]
      keys.value = JSON.parse(JSON.stringify(snapshot.keys))
      meta.value = JSON.parse(JSON.stringify(snapshot.meta))
      selectedKeys.value = []
    }
  }

  return {
    // 状态
    keys,
    meta,
    selectedKeys,
    hoveredKey,
    history,
    currentHistoryIndex,
    dirty,
    saved,
    
    // 计算属性
    canUndo,
    canRedo,
    selectedKey,
    keyboardBounds,
    
    // 动作
    addKey,
    removeKey,
    updateKey,
    selectKey,
    unselectAll,
    markDirty,
    saveSnapshot,
    undo,
    redo
  }
})
