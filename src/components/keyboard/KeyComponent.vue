<template>
  <div
    class="key-component"
    :class="keyClasses"
    :style="keyStyle"
    @click="$emit('click', $event)"
    @mouseenter="$emit('mouseenter')"
    @mouseleave="$emit('mouseleave')"
    @mousedown="startDrag"
  >
    <!-- 按键主体 -->
    <div class="key-body" :style="keyBodyStyle">
      <!-- 按键标签 -->
      <div class="key-labels">
        <div
          v-for="(label, index) in displayLabels"
          :key="index"
          class="key-label"
          :class="`label-${index}`"
          :style="labelStyle"
        >
          {{ label }}
        </div>
      </div>
    </div>
    
    <!-- 选中状态的边框 -->
    <div v-if="isSelected" class="selection-border"></div>
    
    <!-- 悬停状态的高亮 -->
    <div v-if="isHovered && !isSelected" class="hover-highlight"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { KeyData } from '@/stores/keyboard'

interface Props {
  keyData: KeyData
  isSelected: boolean
  isHovered: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'mouseenter'): void
  (e: 'mouseleave'): void
  (e: 'drag', event: { deltaX: number; deltaY: number }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })

// 单位大小（像素）
const UNIT_SIZE = 54

// 计算按键样式
const keyStyle = computed(() => {
  const key = props.keyData
  return {
    position: 'absolute',
    left: `${key.x * UNIT_SIZE}px`,
    top: `${key.y * UNIT_SIZE}px`,
    width: `${key.width * UNIT_SIZE}px`,
    height: `${key.height * UNIT_SIZE}px`,
    transform: key.rotation_angle ? 
      `rotate(${key.rotation_angle}deg)` : 
      'none',
    transformOrigin: key.rotation_x && key.rotation_y ? 
      `${(key.rotation_x - key.x) * UNIT_SIZE}px ${(key.rotation_y - key.y) * UNIT_SIZE}px` : 
      'center',
    zIndex: props.isSelected ? 10 : 1
  }
})

// 计算按键主体样式
const keyBodyStyle = computed(() => {
  const key = props.keyData
  return {
    width: '100%',
    height: '100%',
    backgroundColor: key.color || '#cccccc',
    border: '1px solid rgba(0,0,0,0.2)',
    borderRadius: '4px',
    boxShadow: `
      0 2px 4px rgba(0,0,0,0.1),
      inset 0 1px 0 rgba(255,255,255,0.3),
      inset 0 -1px 0 rgba(0,0,0,0.1)
    `,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  }
})

// 计算标签样式
const labelStyle = computed(() => {
  const key = props.keyData
  return {
    color: key.textColor || '#000000',
    fontSize: '12px',
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: '1.2',
    userSelect: 'none',
    pointerEvents: 'none'
  }
})

// 计算按键类名
const keyClasses = computed(() => {
  return {
    'key-selected': props.isSelected,
    'key-hovered': props.isHovered,
    'key-dragging': isDragging.value,
    'key-stepped': props.keyData.stepped,
    'key-homing': props.keyData.nub
  }
})

// 计算显示的标签
const displayLabels = computed(() => {
  const labels = props.keyData.labels || ['']
  // 最多显示4个标签
  return labels.slice(0, 4).filter(label => label.trim() !== '')
})

// 拖拽功能
const startDrag = (event: MouseEvent) => {
  if (event.button !== 0) return // 只响应左键
  
  isDragging.value = true
  dragStart.value = { x: event.clientX, y: event.clientY }
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value) return
    
    const deltaX = (e.clientX - dragStart.value.x) / UNIT_SIZE
    const deltaY = (e.clientY - dragStart.value.y) / UNIT_SIZE
    
    // 只有移动距离超过阈值才触发拖拽
    if (Math.abs(deltaX) > 0.1 || Math.abs(deltaY) > 0.1) {
      emit('drag', { deltaX, deltaY })
      dragStart.value = { x: e.clientX, y: e.clientY }
    }
  }
  
  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  event.preventDefault()
}
</script>

<style scoped>
.key-component {
  position: relative;
  user-select: none;
}

.key-body {
  transition: all 0.2s ease;
}

.key-component:hover .key-body {
  transform: translateY(-1px);
  box-shadow: 
    0 4px 8px rgba(0,0,0,0.15),
    inset 0 1px 0 rgba(255,255,255,0.4),
    inset 0 -1px 0 rgba(0,0,0,0.1) !important;
}

.key-component:active .key-body {
  transform: translateY(0);
  box-shadow: 
    0 1px 2px rgba(0,0,0,0.2),
    inset 0 1px 0 rgba(255,255,255,0.2),
    inset 0 -1px 0 rgba(0,0,0,0.2) !important;
}

.key-labels {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  padding: 4px;
  gap: 2px;
}

.key-label {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.label-0 {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  font-size: 14px;
  font-weight: 600;
}

.label-1 {
  grid-column: 1;
  grid-row: 1;
  font-size: 10px;
  align-items: flex-start;
  justify-content: flex-start;
}

.label-2 {
  grid-column: 2;
  grid-row: 1;
  font-size: 10px;
  align-items: flex-start;
  justify-content: flex-end;
}

.label-3 {
  grid-column: 1;
  grid-row: 2;
  font-size: 10px;
  align-items: flex-end;
  justify-content: flex-start;
}

/* 多标签时调整主标签 */
.key-labels:has(.label-1) .label-0 {
  grid-column: 1 / -1;
  grid-row: 2;
  font-size: 12px;
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #007bff;
  border-radius: 6px;
  pointer-events: none;
  animation: selection-pulse 2s infinite;
}

.hover-highlight {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid #007bff;
  border-radius: 5px;
  pointer-events: none;
  opacity: 0.6;
}

.key-dragging {
  opacity: 0.8;
  z-index: 20 !important;
}

.key-stepped .key-body {
  border-bottom-width: 3px;
  border-bottom-color: rgba(0,0,0,0.3);
}

.key-homing .key-body::after {
  content: '';
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: rgba(0,0,0,0.3);
  border-radius: 50%;
}

@keyframes selection-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}
</style>
