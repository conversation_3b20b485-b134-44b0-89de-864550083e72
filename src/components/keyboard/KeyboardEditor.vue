<template>
  <div class="keyboard-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-group">
        <button @click="undo" :disabled="!canUndo" title="撤销 (Ctrl+Z)" class="btn btn-icon">
          <span class="icon">↶</span>
        </button>
        <button @click="redo" :disabled="!canRedo" title="重做 (Ctrl+Y)" class="btn btn-icon">
          <span class="icon">↷</span>
        </button>
      </div>
      
      <div class="toolbar-group">
        <button @click="addStandardKey" title="添加按键" class="btn">
          <span class="icon">+</span> 添加按键
        </button>
        <button @click="removeSelectedKeys" :disabled="selectedKeys.length === 0" title="删除选中" class="btn">
          <span class="icon">🗑</span> 删除
        </button>
        <button @click="duplicateSelectedKeys" :disabled="selectedKeys.length === 0" title="复制选中" class="btn">
          <span class="icon">📋</span> 复制
        </button>
      </div>
      
      <div class="toolbar-group">
        <button @click="newKeyboard" title="新建键盘" class="btn">
          <span class="icon">📄</span> 新建
        </button>
        <button @click="exportJson" title="导出JSON" class="btn">
          <span class="icon">💾</span> 导出
        </button>
        <input
          ref="fileInput"
          type="file"
          accept=".json"
          @change="handleImportJson"
          style="display: none"
        />
        <button @click="$refs.fileInput?.click()" title="导入JSON" class="btn">
          <span class="icon">📁</span> 导入
        </button>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 属性面板 -->
      <div class="property-panel">
        <div class="panel-section">
          <h3>键盘属性</h3>
          <div class="form-group">
            <label>名称:</label>
            <input v-model="meta.name" type="text" placeholder="键盘名称" />
          </div>
          <div class="form-group">
            <label>作者:</label>
            <input v-model="meta.author" type="text" placeholder="作者名称" />
          </div>
          <div class="form-group">
            <label>背景色:</label>
            <input v-model="meta.backcolor" type="color" />
          </div>
          <div class="form-group">
            <label>圆角:</label>
            <input v-model="meta.radii" type="text" placeholder="例如: 6px" />
          </div>
        </div>

        <div v-if="selectedKeys.length > 0" class="panel-section">
          <h3>按键属性 ({{ selectedKeys.length }}个选中)</h3>
          <div class="form-group">
            <label>宽度:</label>
            <input
              v-model.number="multiKeyProps.width"
              type="number"
              step="0.25"
              min="0.25"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>高度:</label>
            <input
              v-model.number="multiKeyProps.height"
              type="number"
              step="0.25"
              min="0.25"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>按键颜色:</label>
            <input
              v-model="multiKeyProps.color"
              type="color"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div class="form-group">
            <label>文字颜色:</label>
            <input
              v-model="multiKeyProps.textColor"
              type="color"
              @input="updateSelectedKeyProps"
            />
          </div>
          <div v-if="selectedKeys.length === 1" class="form-group">
            <label>标签:</label>
            <textarea
              v-model="multiKeyProps.labels"
              rows="3"
              placeholder="按键标签，每行一个"
              @input="updateSelectedKeyLabels"
            ></textarea>
          </div>
        </div>

        <div v-if="selectedKeys.length === 0" class="panel-section">
          <h3>快捷操作</h3>
          <button @click="selectAllKeys" class="btn btn-block">全选按键</button>
          <button @click="addRowOfKeys" class="btn btn-block">添加一行按键</button>
        </div>
      </div>

      <!-- 键盘画布 -->
      <div class="keyboard-canvas-container">
        <KeyboardCanvas
          :keys="keys"
          :meta="meta"
          :selected-keys="selectedKeys"
          :hovered-key="hoveredKey"
          @key-click="handleKeyClick"
          @key-hover="handleKeyHover"
          @canvas-click="handleCanvasClick"
          @key-drag="handleKeyDrag"
        />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <span>按键数量: {{ keys.length }}</span>
      <span v-if="selectedKeys.length > 0">已选择: {{ selectedKeys.length }}</span>
      <span v-if="!isSaved" class="unsaved">● 未保存</span>
      <span v-if="keyboardBounds.width > 0">
        尺寸: {{ keyboardBounds.width.toFixed(2) }} × {{ keyboardBounds.height.toFixed(2) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useKeyboard } from '@/composables/useKeyboard'
import { useFileOperations } from '@/composables/useFileOperations'
import KeyboardCanvas from './KeyboardCanvas.vue'
import type { KeyData } from '@/stores/keyboard'

// 组合式函数
const {
  keys,
  selectedKeys,
  meta,
  canUndo,
  canRedo,
  keyboardBounds,
  isDirty,
  isSaved,
  addStandardKey,
  removeSelectedKeys,
  updateSelectedKeys,
  duplicateSelectedKeys,
  selectKey,
  selectAllKeys,
  unselectAll,
  undo,
  redo,
  updateMeta
} = useKeyboard()

const { 
  exportJson, 
  importJson, 
  newKeyboard,
  hasUnsavedChanges 
} = useFileOperations()

// 响应式数据
const fileInput = ref<HTMLInputElement>()
const hoveredKey = ref<KeyData | null>(null)

// 多选按键的属性
const multiKeyProps = ref({
  width: 1,
  height: 1,
  color: '#cccccc',
  textColor: '#000000',
  labels: ''
})

// 监听选中按键变化，更新属性面板
watch(selectedKeys, (newSelection) => {
  if (newSelection.length === 1) {
    const key = newSelection[0]
    multiKeyProps.value = {
      width: key.width,
      height: key.height,
      color: key.color,
      textColor: key.textColor,
      labels: (key.labels || ['']).join('\n')
    }
  } else if (newSelection.length > 1) {
    // 多选时显示第一个按键的属性
    const firstKey = newSelection[0]
    multiKeyProps.value = {
      width: firstKey.width,
      height: firstKey.height,
      color: firstKey.color,
      textColor: firstKey.textColor,
      labels: ''
    }
  }
}, { immediate: true })

// 方法
const updateSelectedKeyProps = () => {
  if (selectedKeys.value.length > 0) {
    updateSelectedKeys({
      width: multiKeyProps.value.width,
      height: multiKeyProps.value.height,
      color: multiKeyProps.value.color,
      textColor: multiKeyProps.value.textColor
    })
  }
}

const updateSelectedKeyLabels = () => {
  if (selectedKeys.value.length === 1) {
    const labels = multiKeyProps.value.labels.split('\n')
    updateSelectedKeys({ labels })
  }
}

const addRowOfKeys = () => {
  const startY = keys.value.length > 0 ? Math.max(...keys.value.map(k => k.y + k.height)) : 0
  
  for (let i = 0; i < 10; i++) {
    addStandardKey()
    if (keys.value.length > 0) {
      const lastKey = keys.value[keys.value.length - 1]
      lastKey.x = i
      lastKey.y = startY
    }
  }
}

const handleKeyClick = (key: KeyData, event: MouseEvent) => {
  selectKey(key, {
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey
  })
}

const handleKeyHover = (key: KeyData | null) => {
  hoveredKey.value = key
}

const handleCanvasClick = (event: MouseEvent) => {
  if (!event.ctrlKey) {
    unselectAll()
  }
}

const handleKeyDrag = (key: KeyData, deltaX: number, deltaY: number) => {
  // 拖拽逻辑将在KeyboardCanvas组件中实现
}

const handleImportJson = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    try {
      await importJson(file)
    } catch (error) {
      alert('导入失败: ' + (error as Error).message)
    }
    target.value = '' // 清空文件输入
  }
}

// 键盘快捷键
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'z':
        event.preventDefault()
        if (event.shiftKey) {
          redo()
        } else {
          undo()
        }
        break
      case 'y':
        event.preventDefault()
        redo()
        break
      case 'a':
        event.preventDefault()
        selectAllKeys()
        break
      case 's':
        event.preventDefault()
        exportJson()
        break
      case 'n':
        event.preventDefault()
        if (hasUnsavedChanges()) {
          if (confirm('有未保存的更改，确定要新建吗？')) {
            newKeyboard()
          }
        } else {
          newKeyboard()
        }
        break
    }
  } else if (event.key === 'Delete' || event.key === 'Backspace') {
    if (selectedKeys.value.length > 0) {
      event.preventDefault()
      removeSelectedKeys()
    }
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.keyboard-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-group {
  display: flex;
  gap: 0.5rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #d0d0d0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  font-weight: 500;
}

.btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #999;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  padding: 0.5rem;
  min-width: 2.5rem;
}

.btn-block {
  width: 100%;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.icon {
  font-size: 1rem;
}

.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.property-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  padding: 1rem;
}

.panel-section {
  margin-bottom: 2rem;
}

.panel-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.keyboard-canvas-container {
  flex: 1;
  overflow: auto;
  background: #fafafa;
}

.status-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: white;
  border-top: 1px solid #e0e0e0;
  font-size: 0.85rem;
  color: #666;
}

.unsaved {
  color: #dc3545;
  font-weight: 600;
}
</style>
