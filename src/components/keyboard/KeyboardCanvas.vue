<template>
  <div class="keyboard-canvas" @click="handleCanvasClick">
    <div 
      id="keyboard-canvas"
      class="keyboard-container"
      :style="keyboardStyle"
    >
      <KeyComponent
        v-for="key in keys"
        :key="key.id"
        :key-data="key"
        :is-selected="isKeySelected(key)"
        :is-hovered="hoveredKey?.id === key.id"
        @click="handleKeyClick(key, $event)"
        @mouseenter="$emit('key-hover', key)"
        @mouseleave="$emit('key-hover', null)"
        @drag="handleKeyDrag(key, $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import KeyComponent from './KeyComponent.vue'
import type { KeyData, KeyboardMeta } from '@/stores/keyboard'

interface Props {
  keys: KeyData[]
  meta: KeyboardMeta
  selectedKeys: KeyData[]
  hoveredKey: KeyData | null
}

interface Emits {
  (e: 'key-click', key: KeyData, event: MouseEvent): void
  (e: 'key-hover', key: KeyData | null): void
  (e: 'canvas-click', event: MouseEvent): void
  (e: 'key-drag', key: KeyData, deltaX: number, deltaY: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算键盘样式
const keyboardStyle = computed(() => {
  // 计算键盘边界
  let minX = 0, minY = 0, maxX = 0, maxY = 0
  
  if (props.keys.length > 0) {
    minX = Math.min(...props.keys.map(k => k.x))
    minY = Math.min(...props.keys.map(k => k.y))
    maxX = Math.max(...props.keys.map(k => k.x + k.width))
    maxY = Math.max(...props.keys.map(k => k.y + k.height))
  }
  
  const width = Math.max(maxX - minX, 10) // 最小宽度
  const height = Math.max(maxY - minY, 5) // 最小高度
  const unit = 54 // 每个单位的像素大小
  
  return {
    width: `${width * unit + 40}px`, // 添加边距
    height: `${height * unit + 40}px`,
    backgroundColor: props.meta.backcolor || '#eeeeee',
    borderRadius: props.meta.radii || '6px',
    position: 'relative',
    margin: '20px',
    padding: '20px',
    minWidth: '600px',
    minHeight: '300px'
  }
})

// 检查按键是否被选中
const isKeySelected = (key: KeyData): boolean => {
  return props.selectedKeys.some(selectedKey => selectedKey.id === key.id)
}

// 处理按键点击
const handleKeyClick = (key: KeyData, event: MouseEvent) => {
  event.stopPropagation()
  emit('key-click', key, event)
}

// 处理画布点击
const handleCanvasClick = (event: MouseEvent) => {
  // 只有点击空白区域才触发
  if (event.target === event.currentTarget || 
      (event.target as HTMLElement).classList.contains('keyboard-container')) {
    emit('canvas-click', event)
  }
}

// 处理按键拖拽
const handleKeyDrag = (key: KeyData, event: { deltaX: number; deltaY: number }) => {
  emit('key-drag', key, event.deltaX, event.deltaY)
}
</script>

<style scoped>
.keyboard-canvas {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;
  background: #fafafa;
  background-image: 
    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.keyboard-container {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.keyboard-container:hover {
  box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}
</style>
