<template>
  <div class="simple-keyboard-editor">
    <h1>Vue Keyboard Layout Editor</h1>
    
    <div class="toolbar">
      <button @click="addKey" class="btn">
        添加按键 ({{ store.keyCount }})
      </button>
      <button
        @click="deleteSelected"
        class="btn btn-danger"
        v-if="store.selectedCount > 0"
      >
        删除选中 ({{ store.selectedCount }})
      </button>
      <button
        @click="store.clearSelection"
        class="btn btn-secondary"
        v-if="store.selectedCount > 0"
      >
        清除选择
      </button>
      <button
        @click="exportLayout"
        class="btn btn-success"
        v-if="store.keyCount > 0"
      >
        导出JSON
      </button>
    </div>

    <div class="canvas">
      <p v-if="store.keyCount === 0">点击"添加按键"开始创建您的键盘布局</p>
      <div v-else class="keyboard-preview">
        <div
          v-for="key in store.keys"
          :key="key.id"
          class="key-preview"
          :class="{ selected: store.selectedKeys.includes(key) }"
          :style="{
            left: key.x * 60 + 'px',
            top: key.y * 60 + 'px',
            backgroundColor: key.color,
            color: key.textColor,
          }"
          @click="store.selectKey(key)"
        >
          {{ key.labels[0] }}
        </div>
      </div>
      <p class="stats">
        键盘: {{ store.meta.name }} | 按键数量: {{ store.keyCount }}
        <span v-if="store.selectedCount > 0">| 已选择: {{ store.selectedCount }}</span>
      </p>
      <div class="help-text" v-if="store.keyCount > 0">
        <p><strong>快捷键:</strong></p>
        <p>• 点击按键选择 • Delete/Backspace删除 • Esc清除选择</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useKeyboardStore } from '@/stores/keyboard'

const store = useKeyboardStore()

const addKey = () => {
  store.addKey()
}

const deleteSelected = () => {
  store.selectedKeys.forEach((key) => {
    store.removeKey(key.id)
  })
}

const exportLayout = () => {
  const data = {
    meta: store.meta,
    keys: store.keys,
  }

  const jsonString = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonString], { type: 'application/json' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = `${store.meta.name || 'keyboard-layout'}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 键盘快捷键
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Delete' || event.key === 'Backspace') {
    if (store.selectedCount > 0) {
      event.preventDefault()
      deleteSelected()
    }
  } else if (event.key === 'Escape') {
    store.clearSelection()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.simple-keyboard-editor {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.simple-keyboard-editor h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 2rem;
}

.toolbar {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background 0.2s;
}

.btn:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-danger {
  background: #dc3545;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-success {
  background: #28a745;
}

.btn-success:hover {
  background: #218838;
}

.canvas {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
  text-align: center;
  color: #6c757d;
  min-height: 400px;
  position: relative;
}

.canvas p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.keyboard-preview {
  position: relative;
  width: 100%;
  height: 300px;
  margin: 1rem 0;
}

.key-preview {
  position: absolute;
  width: 50px;
  height: 50px;
  border: 2px solid #333;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 12px;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.key-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.key-preview.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.stats {
  font-size: 0.9rem;
  color: #495057;
  margin-top: 1rem;
}

.help-text {
  background: #e9ecef;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
  font-size: 0.85rem;
  color: #495057;
}

.help-text p {
  margin: 0.25rem 0;
}

.help-text strong {
  color: #212529;
}
</style>
