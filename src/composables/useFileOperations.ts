import { saveAs } from 'file-saver'
import { useKeyboardStore } from '@/stores/keyboard'

export function useFileOperations() {
  const store = useKeyboardStore()

  // 导出JSON
  const exportJson = () => {
    try {
      // 这里需要使用原项目的序列化逻辑
      // 暂时使用简单的JSON序列化
      const data = {
        meta: store.meta,
        keys: store.keys
      }
      
      const jsonString = JSON.stringify(data, null, 2)
      const blob = new Blob([jsonString], { type: 'application/json' })
      const filename = store.meta.name || 'keyboard-layout'
      saveAs(blob, `${filename}.json`)
      
      store.saved = true
      store.dirty = false
    } catch (error) {
      console.error('导出失败:', error)
      throw new Error('导出JSON文件失败')
    }
  }

  // 导入JSON
  const importJson = (file: File): Promise<void> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (event) => {
        try {
          const result = event.target?.result as string
          const data = JSON.parse(result)
          
          // 保存当前状态到历史
          store.saveSnapshot()
          
          // 导入数据
          if (data.meta) {
            Object.assign(store.meta, data.meta)
          }
          
          if (data.keys && Array.isArray(data.keys)) {
            store.keys = data.keys.map((key, index) => ({
              ...key,
              id: key.id || `imported_key_${Date.now()}_${index}`
            }))
          }
          
          // 清空选择
          store.selectedKeys = []
          store.markDirty()
          
          resolve()
        } catch (error) {
          console.error('导入失败:', error)
          reject(new Error('无效的JSON文件格式'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsText(file)
    })
  }

  // 导出为图片
  const exportImage = async (format: 'png' | 'svg' = 'png') => {
    try {
      if (format === 'png') {
        // 使用html2canvas导出PNG
        const { default: html2canvas } = await import('html2canvas')
        const keyboardElement = document.getElementById('keyboard-canvas')
        
        if (!keyboardElement) {
          throw new Error('找不到键盘画布元素')
        }
        
        const canvas = await html2canvas(keyboardElement, {
          backgroundColor: store.meta.backcolor,
          scale: 2, // 高分辨率
          useCORS: true
        })
        
        canvas.toBlob((blob) => {
          if (blob) {
            const filename = store.meta.name || 'keyboard-layout'
            saveAs(blob, `${filename}.png`)
          }
        })
      } else {
        // 导出SVG (需要实现SVG渲染逻辑)
        throw new Error('SVG导出功能待实现')
      }
    } catch (error) {
      console.error('导出图片失败:', error)
      throw new Error('导出图片失败')
    }
  }

  // 新建键盘
  const newKeyboard = () => {
    store.saveSnapshot()
    
    // 重置为默认状态
    store.keys = []
    store.meta = {
      backcolor: '#eeeeee',
      name: '',
      author: '',
      notes: '',
      background: { name: '', style: '' },
      radii: '',
      css: ''
    }
    store.selectedKeys = []
    store.dirty = false
    store.saved = true
  }

  // 加载预设布局
  const loadPreset = async (presetName: string) => {
    try {
      const response = await fetch(`/assets/samples/${presetName}.json`)
      if (!response.ok) {
        throw new Error('预设文件不存在')
      }
      
      const data = await response.json()
      
      store.saveSnapshot()
      
      // 导入预设数据
      if (data.meta) {
        Object.assign(store.meta, data.meta)
      }
      
      if (data.keys && Array.isArray(data.keys)) {
        store.keys = data.keys.map((key, index) => ({
          ...key,
          id: key.id || `preset_key_${Date.now()}_${index}`
        }))
      }
      
      store.selectedKeys = []
      store.markDirty()
    } catch (error) {
      console.error('加载预设失败:', error)
      throw new Error('加载预设布局失败')
    }
  }

  // 获取可用的预设列表
  const getAvailablePresets = async (): Promise<string[]> => {
    try {
      // 这里应该从服务器获取预设列表
      // 暂时返回硬编码的列表
      return [
        'apple-wireless',
        'pkb',
        'stealth-black'
      ]
    } catch (error) {
      console.error('获取预设列表失败:', error)
      return []
    }
  }

  // 保存到本地存储
  const saveToLocalStorage = (key: string = 'keyboard-layout') => {
    try {
      const data = {
        meta: store.meta,
        keys: store.keys,
        timestamp: Date.now()
      }
      
      localStorage.setItem(key, JSON.stringify(data))
      store.saved = true
      store.dirty = false
    } catch (error) {
      console.error('保存到本地存储失败:', error)
      throw new Error('保存失败')
    }
  }

  // 从本地存储加载
  const loadFromLocalStorage = (key: string = 'keyboard-layout') => {
    try {
      const dataString = localStorage.getItem(key)
      if (!dataString) {
        throw new Error('没有找到保存的数据')
      }
      
      const data = JSON.parse(dataString)
      
      store.saveSnapshot()
      
      if (data.meta) {
        Object.assign(store.meta, data.meta)
      }
      
      if (data.keys && Array.isArray(data.keys)) {
        store.keys = data.keys.map((key, index) => ({
          ...key,
          id: key.id || `local_key_${Date.now()}_${index}`
        }))
      }
      
      store.selectedKeys = []
      store.markDirty()
    } catch (error) {
      console.error('从本地存储加载失败:', error)
      throw new Error('加载失败')
    }
  }

  // 检查是否有未保存的更改
  const hasUnsavedChanges = () => {
    return store.dirty && !store.saved
  }

  return {
    exportJson,
    importJson,
    exportImage,
    newKeyboard,
    loadPreset,
    getAvailablePresets,
    saveToLocalStorage,
    loadFromLocalStorage,
    hasUnsavedChanges
  }
}
