import { computed } from 'vue'
import { useKeyboardStore, type KeyData } from '@/stores/keyboard'

export function useKeyboard() {
  const store = useKeyboardStore()

  // 计算属性
  const keys = computed(() => store.keys)
  const selectedKeys = computed(() => store.selectedKeys)
  const meta = computed(() => store.meta)
  const canUndo = computed(() => store.canUndo)
  const canRedo = computed(() => store.canRedo)
  const keyboardBounds = computed(() => store.keyboardBounds)
  const isDirty = computed(() => store.dirty)
  const isSaved = computed(() => store.saved)

  // 按键操作
  const addKey = (keyData?: Partial<KeyData>) => {
    store.saveSnapshot()
    return store.addKey(keyData || {})
  }

  const addStandardKey = () => {
    const newKeyData: Partial<KeyData> = {
      x: 0,
      y: 0,
      width: 1,
      height: 1,
      labels: [''],
      color: '#cccccc',
      textColor: '#000000'
    }
    
    // 如果有选中的按键，在其旁边添加
    if (store.selectedKeys.length > 0) {
      const lastSelected = store.selectedKeys[store.selectedKeys.length - 1]
      newKeyData.x = lastSelected.x + lastSelected.width
      newKeyData.y = lastSelected.y
      newKeyData.color = lastSelected.color
      newKeyData.textColor = lastSelected.textColor
    }
    
    return addKey(newKeyData)
  }

  const removeSelectedKeys = () => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.removeKey(key.id)
      })
    }
  }

  const updateSelectedKeys = (updates: Partial<KeyData>) => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.updateKey(key.id, updates)
      })
    }
  }

  const duplicateSelectedKeys = () => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      const newKeys: KeyData[] = []
      
      store.selectedKeys.forEach(key => {
        const duplicatedKey = {
          ...key,
          x: key.x + 1, // 向右偏移1个单位
          y: key.y
        }
        delete duplicatedKey.id // 让addKey生成新的ID
        const newKey = store.addKey(duplicatedKey)
        newKeys.push(newKey)
      })
      
      // 选中新复制的按键
      store.selectedKeys = newKeys
    }
  }

  // 选择操作
  const selectKey = (key: KeyData, options: { ctrlKey?: boolean; shiftKey?: boolean; altKey?: boolean } = {}) => {
    store.selectKey(key, options)
  }

  const selectAllKeys = () => {
    store.selectedKeys = [...store.keys]
  }

  const unselectAll = () => {
    store.unselectAll()
  }

  // 移动操作
  const moveSelectedKeys = (deltaX: number, deltaY: number) => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.updateKey(key.id, {
          x: key.x + deltaX,
          y: key.y + deltaY
        })
      })
    }
  }

  const resizeSelectedKeys = (deltaWidth: number, deltaHeight: number) => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.updateKey(key.id, {
          width: Math.max(0.25, key.width + deltaWidth),
          height: Math.max(0.25, key.height + deltaHeight)
        })
      })
    }
  }

  const rotateSelectedKeys = (deltaAngle: number) => {
    if (store.selectedKeys.length > 0) {
      store.saveSnapshot()
      store.selectedKeys.forEach(key => {
        store.updateKey(key.id, {
          rotation_angle: (key.rotation_angle || 0) + deltaAngle
        })
      })
    }
  }

  // 历史操作
  const undo = () => {
    store.undo()
  }

  const redo = () => {
    store.redo()
  }

  // 键盘元数据操作
  const updateMeta = (updates: Partial<typeof store.meta>) => {
    store.saveSnapshot()
    Object.assign(store.meta, updates)
    store.markDirty()
  }

  // 工具函数
  const getKeyAt = (x: number, y: number): KeyData | null => {
    // 从后往前查找，因为后面的按键在上层
    for (let i = store.keys.length - 1; i >= 0; i--) {
      const key = store.keys[i]
      if (x >= key.x && x <= key.x + key.width &&
          y >= key.y && y <= key.y + key.height) {
        return key
      }
    }
    return null
  }

  const getKeysInRect = (x: number, y: number, width: number, height: number): KeyData[] => {
    return store.keys.filter(key => {
      return !(key.x + key.width < x || 
               key.x > x + width || 
               key.y + key.height < y || 
               key.y > y + height)
    })
  }

  return {
    // 状态
    keys,
    selectedKeys,
    meta,
    canUndo,
    canRedo,
    keyboardBounds,
    isDirty,
    isSaved,
    
    // 按键操作
    addKey,
    addStandardKey,
    removeSelectedKeys,
    updateSelectedKeys,
    duplicateSelectedKeys,
    
    // 选择操作
    selectKey,
    selectAllKeys,
    unselectAll,
    
    // 移动操作
    moveSelectedKeys,
    resizeSelectedKeys,
    rotateSelectedKeys,
    
    // 历史操作
    undo,
    redo,
    
    // 元数据操作
    updateMeta,
    
    // 工具函数
    getKeyAt,
    getKeysInRect
  }
}
