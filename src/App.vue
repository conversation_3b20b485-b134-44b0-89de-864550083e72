<script setup lang="ts">
import SimpleKeyboardEditor from "./components/SimpleKeyboardEditor.vue";
</script>

<template>
  <div id="app">
    <SimpleKeyboardEditor />
  </div>
</template>

<style>
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}
</style>
