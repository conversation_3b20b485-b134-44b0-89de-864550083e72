[{"name": "Named HTML Entities", "href": "https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references", "description": "This is the set of all printable 'named' character entities supported by HTML.", "glyphs": [{"name": "no-break space (non-breaking space)", "html": "&nbsp;"}, {"name": "quotation mark (APL quote)", "html": "&quot;"}, {"name": "apostrophe (apostrophe-quote)", "html": "&apos;"}, {"name": "ampersand", "html": "&amp;"}, {"name": "less-than sign", "html": "&lt;"}, {"name": "greater-than sign", "html": "&gt;"}, {"name": "inverted exclamation mark", "html": "&iexcl;"}, {"name": "cent sign", "html": "&cent;"}, {"name": "pound sign", "html": "&pound;"}, {"name": "currency sign", "html": "&curren;"}, {"name": "yen sign (yuan sign)", "html": "&yen;"}, {"name": "broken bar (broken vertical bar)", "html": "&brvbar;"}, {"name": "section sign", "html": "&sect;"}, {"name": "diaeresis (spacing diaeresis)", "html": "&uml;"}, {"name": "copyright symbol", "html": "&copy;"}, {"name": "registered sign (registered trademark symbol)", "html": "&reg;"}, {"name": "trademark symbol", "html": "&trade;"}, {"name": "feminine ordinal indicator", "html": "&ordf;"}, {"name": "left-pointing double angle quotation mark (left pointing guillemet)", "html": "&laquo;"}, {"name": "right-pointing double angle quotation mark (right pointing guillemet)", "html": "&raquo;"}, {"name": "not sign", "html": "&not;"}, {"name": "macron (spacing macron, overline, APL overbar)", "html": "&macr;"}, {"name": "degree symbol", "html": "&deg;"}, {"name": "plus-minus sign (plus-or-minus sign)", "html": "&plusmn;"}, {"name": "superscript one (superscript digit one)", "html": "&sup1;"}, {"name": "superscript two (superscript digit two, squared)", "html": "&sup2;"}, {"name": "superscript three (superscript digit three, cubed)", "html": "&sup3;"}, {"name": "acute accent (spacing acute)", "html": "&acute;"}, {"name": "micro sign", "html": "&micro;"}, {"name": "pilcrow sign (paragraph sign)", "html": "&para;"}, {"name": "middle dot (Georgian comma, Greek middle dot)", "html": "&middot;"}, {"name": "cedilla (spacing cedilla)", "html": "&cedil;"}, {"name": "masculine ordinal indicator", "html": "&ordm;"}, {"name": "vulgar fraction one quarter (fraction one quarter)", "html": "&frac14;"}, {"name": "vulgar fraction one half (fraction one half)", "html": "&frac12;"}, {"name": "vulgar fraction three quarters (fraction three quarters)", "html": "&frac34;"}, {"name": "inverted question mark (turned question mark)", "html": "&iquest;"}, {"name": "Latin capital letter A with grave accent (Latin capital letter A grave)", "html": "&Agrave;"}, {"name": "Latin capital letter A with acute accent", "html": "&Aacute;"}, {"name": "Latin capital letter A with circumflex", "html": "&Acirc;"}, {"name": "Latin capital letter A with tilde", "html": "&Atilde;"}, {"name": "Latin capital letter A with diaeresis", "html": "&Auml;"}, {"name": "Latin capital letter A with ring above (Latin capital letter A ring)", "html": "&Aring;"}, {"name": "Latin capital letter AE (Latin capital ligature AE)", "html": "&AElig;"}, {"name": "Latin capital letter C with cedilla", "html": "&Ccedil;"}, {"name": "Latin capital letter E with grave accent", "html": "&Egrave;"}, {"name": "Latin capital letter E with acute accent", "html": "&Eacute;"}, {"name": "Latin capital letter E with circumflex", "html": "&Ecirc;"}, {"name": "Latin capital letter E with diaeresis", "html": "&Euml;"}, {"name": "Latin capital letter I with grave accent", "html": "&Igrave;"}, {"name": "Latin capital letter I with acute accent", "html": "&Iacute;"}, {"name": "Latin capital letter I with circumflex", "html": "&Icirc;"}, {"name": "Latin capital letter I with diaeresis", "html": "&Iuml;"}, {"name": "Latin capital letter Eth", "html": "&ETH;"}, {"name": "Latin capital letter N with tilde", "html": "&Ntilde;"}, {"name": "Latin capital letter O with grave accent", "html": "&Ograve;"}, {"name": "Latin capital letter O with acute accent", "html": "&Oacute;"}, {"name": "Latin capital letter O with circumflex", "html": "&Ocirc;"}, {"name": "Latin capital letter O with tilde", "html": "&Otilde;"}, {"name": "Latin capital letter O with diaeresis", "html": "&Ouml;"}, {"name": "multiplication sign", "html": "&times;"}, {"name": "Latin capital letter O with stroke (Latin capital letter O slash)", "html": "&Oslash;"}, {"name": "Latin capital letter U with grave accent", "html": "&Ugrave;"}, {"name": "Latin capital letter U with acute accent", "html": "&Uacute;"}, {"name": "Latin capital letter U with circumflex", "html": "&Ucirc;"}, {"name": "Latin capital letter U with diaeresis", "html": "&Uuml;"}, {"name": "Latin capital letter Y with acute accent", "html": "&Yacute;"}, {"name": "Latin capital letter THORN", "html": "&THORN;"}, {"name": "Latin small letter sharp s (ess-zed)", "html": "&szlig;"}, {"name": "Latin small letter a with grave accent", "html": "&agrave;"}, {"name": "Latin small letter a with acute accent", "html": "&aacute;"}, {"name": "Latin small letter a with circumflex", "html": "&acirc;"}, {"name": "Latin small letter a with tilde", "html": "&atilde;"}, {"name": "Latin small letter a with diaeresis", "html": "&auml;"}, {"name": "Latin small letter a with ring above", "html": "&aring;"}, {"name": "Latin small letter ae (Latin small ligature ae)", "html": "&aelig;"}, {"name": "Latin small letter c with cedilla", "html": "&ccedil;"}, {"name": "Latin small letter e with grave accent", "html": "&egrave;"}, {"name": "Latin small letter e with acute accent", "html": "&eacute;"}, {"name": "Latin small letter e with circumflex", "html": "&ecirc;"}, {"name": "Latin small letter e with diaeresis", "html": "&euml;"}, {"name": "Latin small letter i with grave accent", "html": "&igrave;"}, {"name": "Latin small letter i with acute accent", "html": "&iacute;"}, {"name": "Latin small letter i with circumflex", "html": "&icirc;"}, {"name": "Latin small letter i with diaeresis", "html": "&iuml;"}, {"name": "Latin small letter eth", "html": "&eth;"}, {"name": "Latin small letter n with tilde", "html": "&ntilde;"}, {"name": "Latin small letter o with grave accent", "html": "&ograve;"}, {"name": "Latin small letter o with acute accent", "html": "&oacute;"}, {"name": "Latin small letter o with circumflex", "html": "&ocirc;"}, {"name": "Latin small letter o with tilde", "html": "&otilde;"}, {"name": "Latin small letter o with diaeresis", "html": "&ouml;"}, {"name": "division sign (obelus)", "html": "&divide;"}, {"name": "Latin small letter o with stroke (Latin small letter o slash)", "html": "&oslash;"}, {"name": "Latin small letter u with grave accent", "html": "&ugrave;"}, {"name": "Latin small letter u with acute accent", "html": "&uacute;"}, {"name": "Latin small letter u with circumflex", "html": "&ucirc;"}, {"name": "Latin small letter u with diaeresis", "html": "&uuml;"}, {"name": "Latin small letter y with acute accent", "html": "&yacute;"}, {"name": "Latin small letter thorn", "html": "&thorn;"}, {"name": "Latin small letter y with diaeresis", "html": "&yuml;"}, {"name": "Latin capital ligature oe", "html": "&OElig;"}, {"name": "Latin small ligature oe", "html": "&oelig;"}, {"name": "Latin capital letter s with caron", "html": "&Scaron;"}, {"name": "Latin small letter s with caron", "html": "&scaron;"}, {"name": "Latin capital letter y with diaeresis", "html": "&Yuml;"}, {"name": "Latin small letter f with hook (function, florin)", "html": "&fnof;"}, {"name": "modifier letter circumflex accent", "html": "&circ;"}, {"name": "small tilde", "html": "&tilde;"}, {"name": "Greek capital letter Alpha", "html": "&Alpha;"}, {"name": "Greek capital letter Beta", "html": "&Beta;"}, {"name": "Greek capital letter Gamma", "html": "&Gamma;"}, {"name": "Greek capital letter Delta", "html": "&Delta;"}, {"name": "Greek capital letter Epsilon", "html": "&Epsilon;"}, {"name": "Greek capital letter Zeta", "html": "&Zeta;"}, {"name": "Greek capital letter Eta", "html": "&Eta;"}, {"name": "Greek capital letter Theta", "html": "&Theta;"}, {"name": "Greek capital letter Iota", "html": "&Iota;"}, {"name": "Greek capital letter Kappa", "html": "&Kappa;"}, {"name": "Greek capital letter Lambda", "html": "&Lambda;"}, {"name": "Greek capital letter Mu", "html": "&Mu;"}, {"name": "Greek capital letter Nu", "html": "&Nu;"}, {"name": "Greek capital letter Xi", "html": "&Xi;"}, {"name": "Greek capital letter Omicron", "html": "&Omicron;"}, {"name": "Greek capital letter Pi", "html": "&Pi;"}, {"name": "Greek capital letter Rho", "html": "&Rho;"}, {"name": "Greek capital letter Sigma", "html": "&Sigma;"}, {"name": "Greek capital letter Tau", "html": "&Tau;"}, {"name": "Greek capital letter Upsilon", "html": "&Upsilon;"}, {"name": "Greek capital letter Phi", "html": "&Phi;"}, {"name": "Greek capital letter Chi", "html": "&Chi;"}, {"name": "Greek capital letter Psi", "html": "&Psi;"}, {"name": "Greek capital letter Omega", "html": "&Omega;"}, {"name": "Greek small letter alpha", "html": "&alpha;"}, {"name": "Greek small letter beta", "html": "&beta;"}, {"name": "Greek small letter gamma", "html": "&gamma;"}, {"name": "Greek small letter delta", "html": "&delta;"}, {"name": "Greek small letter epsilon", "html": "&epsilon;"}, {"name": "Greek small letter zeta", "html": "&zeta;"}, {"name": "Greek small letter eta", "html": "&eta;"}, {"name": "Greek small letter theta", "html": "&theta;"}, {"name": "Greek small letter iota", "html": "&iota;"}, {"name": "Greek small letter kappa", "html": "&kappa;"}, {"name": "Greek small letter lambda", "html": "&lambda;"}, {"name": "Greek small letter mu", "html": "&mu;"}, {"name": "Greek small letter nu", "html": "&nu;"}, {"name": "Greek small letter xi", "html": "&xi;"}, {"name": "Greek small letter omicron", "html": "&omicron;"}, {"name": "Greek small letter pi", "html": "&pi;"}, {"name": "Greek small letter rho", "html": "&rho;"}, {"name": "Greek small letter final sigma", "html": "&sigmaf;"}, {"name": "Greek small letter sigma", "html": "&sigma;"}, {"name": "Greek small letter tau", "html": "&tau;"}, {"name": "Greek small letter upsilon", "html": "&upsilon;"}, {"name": "Greek small letter phi", "html": "&phi;"}, {"name": "Greek small letter chi", "html": "&chi;"}, {"name": "Greek small letter psi", "html": "&psi;"}, {"name": "Greek small letter omega", "html": "&omega;"}, {"name": "Greek theta symbol", "html": "&thetasym;"}, {"name": "Greek Upsilon with hook symbol", "html": "&upsih;"}, {"name": "Greek pi symbol", "html": "&piv;"}, {"name": "en dash", "html": "&ndash;"}, {"name": "em dash", "html": "&mdash;"}, {"name": "left single quotation mark", "html": "&lsquo;"}, {"name": "right single quotation mark", "html": "&rsquo;"}, {"name": "single low-9 quotation mark", "html": "&sbquo;"}, {"name": "left double quotation mark", "html": "&ldquo;"}, {"name": "right double quotation mark", "html": "&rdquo;"}, {"name": "double low-9 quotation mark", "html": "&bdquo;"}, {"name": "dagger, obelisk", "html": "&dagger;"}, {"name": "double dagger, double obelisk", "html": "&Dagger;"}, {"name": "bullet (black small circle)", "html": "&bull;"}, {"name": "horizontal ellipsis (three dot leader)", "html": "&hellip;"}, {"name": "per mille sign", "html": "&permil;"}, {"name": "prime (minutes, feet)", "html": "&prime;"}, {"name": "double prime (seconds, inches)", "html": "&Prime;"}, {"name": "single left-pointing angle quotation mark", "html": "&lsaquo;"}, {"name": "single right-pointing angle quotation mark", "html": "&rsaquo;"}, {"name": "overline (spacing overscore)", "html": "&oline;"}, {"name": "fraction slash (solidus)", "html": "&frasl;"}, {"name": "euro sign", "html": "&euro;"}, {"name": "black-letter capital I (imaginary part)", "html": "&image;"}, {"name": "script capital P (power set, <PERSON><PERSON><PERSON><PERSON> p)", "html": "&weierp;"}, {"name": "black-letter capital R (real part symbol)", "html": "&real;"}, {"name": "alef symbol (first transfinite cardinal)", "html": "&alefsym;"}, {"name": "leftwards arrow", "html": "&larr;"}, {"name": "upwards arrow", "html": "&uarr;"}, {"name": "rightwards arrow", "html": "&rarr;"}, {"name": "downwards arrow", "html": "&darr;"}, {"name": "left right arrow", "html": "&harr;"}, {"name": "downwards arrow with corner leftwards (carriage return)", "html": "&crarr;"}, {"name": "leftwards double arrow", "html": "&lArr;"}, {"name": "upwards double arrow", "html": "&uArr;"}, {"name": "rightwards double arrow", "html": "&rArr;"}, {"name": "downwards double arrow", "html": "&dArr;"}, {"name": "left right double arrow", "html": "&hArr;"}, {"name": "for all", "html": "&forall;"}, {"name": "partial differential", "html": "&part;"}, {"name": "there exists", "html": "&exist;"}, {"name": "empty set (null set)", "html": "&empty;"}, {"name": "del or nabla (vector differential operator)", "html": "&nabla;"}, {"name": "element of", "html": "&isin;"}, {"name": "not an element of", "html": "&notin;"}, {"name": "contains as member", "html": "&ni;"}, {"name": "n-ary product (product sign)", "html": "&prod;"}, {"name": "n-ary summation", "html": "&sum;"}, {"name": "minus sign", "html": "&minus;"}, {"name": "asterisk operator", "html": "&lowast;"}, {"name": "square root (radical sign)", "html": "&radic;"}, {"name": "proportional to", "html": "&prop;"}, {"name": "infinity", "html": "&infin;"}, {"name": "angle", "html": "&ang;"}, {"name": "logical and (wedge)", "html": "&and;"}, {"name": "logical or (vee)", "html": "&or;"}, {"name": "intersection (cap)", "html": "&cap;"}, {"name": "union (cup)", "html": "&cup;"}, {"name": "integral", "html": "&int;"}, {"name": "therefore sign", "html": "&there4;"}, {"name": "tilde operator (varies with, similar to)", "html": "&sim;"}, {"name": "congruent to", "html": "&cong;"}, {"name": "almost equal to (asymptotic to)", "html": "&asymp;"}, {"name": "not equal to", "html": "&ne;"}, {"name": "identical to; equivalent to", "html": "&equiv;"}, {"name": "less-than or equal to", "html": "&le;"}, {"name": "greater-than or equal to", "html": "&ge;"}, {"name": "subset of", "html": "&sub;"}, {"name": "superset of", "html": "&sup;"}, {"name": "not a subset of", "html": "&nsub;"}, {"name": "subset of or equal to", "html": "&sube;"}, {"name": "superset of or equal to", "html": "&supe;"}, {"name": "circled plus (direct sum)", "html": "&oplus;"}, {"name": "circled times (vector product)", "html": "&otimes;"}, {"name": "up tack (orthogonal to, perpendicular)", "html": "&perp;"}, {"name": "dot operator", "html": "&sdot;"}, {"name": "left ceiling (APL upstile)", "html": "&lceil;"}, {"name": "right ceiling", "html": "&rceil;"}, {"name": "left floor (APL downstile)", "html": "&lfloor;"}, {"name": "right floor", "html": "&rfloor;"}, {"name": "left-pointing angle bracket (bra)", "html": "&lang;"}, {"name": "right-pointing angle bracket (ket)", "html": "&rang;"}, {"name": "lozenge", "html": "&loz;"}, {"name": "black spade suit", "html": "&spades;"}, {"name": "black club suit (shamrock)", "html": "&clubs;"}, {"name": "black heart suit (valentine)", "html": "&hearts;"}, {"name": "black diamond suit", "html": "&diams;"}]}, {"name": "Combining Diacritical Marks", "href": "http://unicode-table.com/en/blocks/combining-diacritical-marks/", "description": "This is a selection of various 'combining' characters superimposed on top of a small circle or rectangle, as commonly seen on foreign-language keyboards.", "glyphs": [{"name": "Combining Grave Accent", "html": "<span class='cd'>&#9676;&#768;</span>"}, {"name": "Combining Acute Accent", "html": "<span class='cd'>&#9676;&#769;</span>"}, {"name": "Combining Circumflex Accent", "html": "<span class='cd'>&#9676;&#770;</span>"}, {"name": "Combining Tilde", "html": "<span class='cd'>&#9676;&#771;</span>"}, {"name": "Combining Macron", "html": "<span class='cd'>&#9676;&#772;</span>"}, {"name": "Combining Overline", "html": "<span class='cd'>&#9676;&#773;</span>"}, {"name": "Combining Breve", "html": "<span class='cd'>&#9676;&#774;</span>"}, {"name": "Combining Dot Above", "html": "<span class='cd'>&#9676;&#775;</span>"}, {"name": "Combining Diaeresis", "html": "<span class='cd'>&#9676;&#776;</span>"}, {"name": "Combining Hook Above", "html": "<span class='cd'>&#9676;&#777;</span>"}, {"name": "Combining Ring Above", "html": "<span class='cd'>&#9676;&#778;</span>"}, {"name": "Combining Double Acute Accent", "html": "<span class='cd'>&#9676;&#779;</span>"}, {"name": "Combining <PERSON>", "html": "<span class='cd'>&#9676;&#780;</span>"}, {"name": "Combining Vertical Line Above", "html": "<span class='cd'>&#9676;&#781;</span>"}, {"name": "Combining Double Vertical Line Above", "html": "<span class='cd'>&#9676;&#782;</span>"}, {"name": "Combining Double Grave Accent", "html": "<span class='cd'>&#9676;&#783;</span>"}, {"name": "Combining Candrabindu", "html": "<span class='cd'>&#9676;&#784;</span>"}, {"name": "Combining Inverted Breve", "html": "<span class='cd'>&#9676;&#785;</span>"}, {"name": "Combining Turned Comma Above", "html": "<span class='cd'>&#9676;&#786;</span>"}, {"name": "Combining Comma Above", "html": "<span class='cd'>&#9676;&#787;</span>"}, {"name": "Combining Reversed Comma Above", "html": "<span class='cd'>&#9676;&#788;</span>"}, {"name": "Combining Comma Above Right", "html": "<span class='cd'>&#9676;&#789;</span>"}, {"name": "Combining Grave Accent Below", "html": "<span class='cd'>&#9676;&#790;</span>"}, {"name": "Combining Acute Accent Below", "html": "<span class='cd'>&#9676;&#791;</span>"}, {"name": "Combining Left Tack Below", "html": "<span class='cd'>&#9676;&#792;</span>"}, {"name": "Combining Right Tack Below", "html": "<span class='cd'>&#9676;&#793;</span>"}, {"name": "Combining Left Angle Above", "html": "<span class='cd'>&#9676;&#794;</span>"}, {"name": "Combining Horn", "html": "<span class='cd'>&#9676;&#795;</span>"}, {"name": "Combining Left Half Ring Below", "html": "<span class='cd'>&#9676;&#796;</span>"}, {"name": "Combining UP Tack Below", "html": "<span class='cd'>&#9676;&#797;</span>"}, {"name": "Combining Down Tack Below", "html": "<span class='cd'>&#9676;&#798;</span>"}, {"name": "Combining Plus Sign Below", "html": "<span class='cd'>&#9676;&#799;</span>"}, {"name": "Combining Minus Sign Below", "html": "<span class='cd'>&#9676;&#800;</span>"}, {"name": "Combining Palatalized Hook Below", "html": "<span class='cd'>&#9676;&#801;</span>"}, {"name": "Combining Retroflex Hook Below", "html": "<span class='cd'>&#9676;&#802;</span>"}, {"name": "Combining Dot Below", "html": "<span class='cd'>&#9676;&#803;</span>"}, {"name": "Combining Diaeresis Below", "html": "<span class='cd'>&#9676;&#804;</span>"}, {"name": "Combining Ring Below", "html": "<span class='cd'>&#9676;&#805;</span>"}, {"name": "Combining Comma Below", "html": "<span class='cd'>&#9676;&#806;</span>"}, {"name": "Combining Cedilla", "html": "<span class='cd'>&#9676;&#807;</span>"}, {"name": "Combining Ogonek", "html": "<span class='cd'>&#9676;&#808;</span>"}, {"name": "Combining Vertical Line Below", "html": "<span class='cd'>&#9676;&#809;</span>"}, {"name": "Combining Bridge Below", "html": "<span class='cd'>&#9676;&#810;</span>"}, {"name": "Combining Inverted Double ARCH Below", "html": "<span class='cd'>&#9676;&#811;</span>"}, {"name": "Combining Caron Below", "html": "<span class='cd'>&#9676;&#812;</span>"}, {"name": "Combining Circumflex Accent Below", "html": "<span class='cd'>&#9676;&#813;</span>"}, {"name": "Combining Breve Below", "html": "<span class='cd'>&#9676;&#814;</span>"}, {"name": "Combining Inverted Breve Below", "html": "<span class='cd'>&#9676;&#815;</span>"}, {"name": "Combining Tilde Below", "html": "<span class='cd'>&#9676;&#816;</span>"}, {"name": "Combining Macron Below", "html": "<span class='cd'>&#9676;&#817;</span>"}, {"name": "Combining Low Line", "html": "<span class='cd'>&#9676;&#818;</span>"}, {"name": "Combining Double Low Line", "html": "<span class='cd'>&#9676;&#819;</span>"}, {"name": "Combining Tilde Overlay", "html": "<span class='cd'>&#9676;&#820;</span>"}, {"name": "Combining Short Stroke Overlay", "html": "<span class='cd'>&#9676;&#821;</span>"}, {"name": "Combining Long Stroke Overlay", "html": "<span class='cd'>&#9676;&#822;</span>"}, {"name": "Combining Short Solidus Overlay", "html": "<span class='cd'>&#9676;&#823;</span>"}, {"name": "Combining Long Solidus Overlay", "html": "<span class='cd'>&#9676;&#824;</span>"}, {"name": "Combining Right Half Ring Below", "html": "<span class='cd'>&#9676;&#825;</span>"}, {"name": "Combining Inverted Bridge Below", "html": "<span class='cd'>&#9676;&#826;</span>"}, {"name": "Combining Square Below", "html": "<span class='cd'>&#9676;&#827;</span>"}, {"name": "Combining Seagull Below", "html": "<span class='cd'>&#9676;&#828;</span>"}, {"name": "Combining X Above", "html": "<span class='cd'>&#9676;&#829;</span>"}, {"name": "Combining Vertical Tilde", "html": "<span class='cd'>&#9676;&#830;</span>"}, {"name": "Combining Double Overline", "html": "<span class='cd'>&#9676;&#831;</span>"}, {"name": "Combining Grave Tone <PERSON>", "html": "<span class='cd'>&#9676;&#832;</span>"}, {"name": "Combining Acute Tone Mark", "html": "<span class='cd'>&#9676;&#833;</span>"}, {"name": "Combining Greek Perispomeni", "html": "<span class='cd'>&#9676;&#834;</span>"}, {"name": "Combining Greek Ko<PERSON>is", "html": "<span class='cd'>&#9676;&#835;</span>"}, {"name": "Combining Greek Dialytika Tonos", "html": "<span class='cd'>&#9676;&#836;</span>"}, {"name": "Combining Greek Ypogegrammeni", "html": "<span class='cd'>&#9676;&#837;</span>"}, {"name": "Combining Bridge Above", "html": "<span class='cd'>&#9676;&#838;</span>"}, {"name": "Combining Equals Sign Below", "html": "<span class='cd'>&#9676;&#839;</span>"}, {"name": "Combining Double Vertical Line Below", "html": "<span class='cd'>&#9676;&#840;</span>"}, {"name": "Combining Left Angle Below", "html": "<span class='cd'>&#9676;&#841;</span>"}, {"name": "Combining Not Tilde Above", "html": "<span class='cd'>&#9676;&#842;</span>"}, {"name": "Combining Homothetic Above", "html": "<span class='cd'>&#9676;&#843;</span>"}, {"name": "Combining Almost Equal To Above", "html": "<span class='cd'>&#9676;&#844;</span>"}, {"name": "Combining Left Right Arrow Below", "html": "<span class='cd'>&#9676;&#845;</span>"}, {"name": "Combining Upwards Arrow Below", "html": "<span class='cd'>&#9676;&#846;</span>"}, {"name": "Combining Right Arrowhead Above", "html": "<span class='cd'>&#9676;&#848;</span>"}, {"name": "Combining Left Half Ring Above", "html": "<span class='cd'>&#9676;&#849;</span>"}, {"name": "Combining Fermata", "html": "<span class='cd'>&#9676;&#850;</span>"}, {"name": "Combining X Below", "html": "<span class='cd'>&#9676;&#851;</span>"}, {"name": "Combining Left Arrowhead Below", "html": "<span class='cd'>&#9676;&#852;</span>"}, {"name": "Combining Right Arrowhead Below", "html": "<span class='cd'>&#9676;&#853;</span>"}, {"name": "Combining Right Arrowhead and Up Arrowhead Below", "html": "<span class='cd'>&#9676;&#854;</span>"}, {"name": "Combining Right Half Ring Above", "html": "<span class='cd'>&#9676;&#855;</span>"}, {"name": "Combining Dot Above Right", "html": "<span class='cd'>&#9676;&#856;</span>"}, {"name": "Combining Asterisk Below", "html": "<span class='cd'>&#9676;&#857;</span>"}, {"name": "Combining Double Ring Below", "html": "<span class='cd'>&#9676;&#858;</span>"}, {"name": "Combining Zigzag Above", "html": "<span class='cd'>&#9676;&#859;</span>"}, {"name": "Combining Double Breve Below", "html": "<span class='cd'>&#9676;&#860;</span>"}, {"name": "Combining Double Breve", "html": "<span class='cd'>&#9676;&#861;</span>"}, {"name": "Combining Double Macron", "html": "<span class='cd'>&#9676;&#862;</span>"}, {"name": "Combining Double Macron Below", "html": "<span class='cd'>&#9676;&#863;</span>"}, {"name": "Combining Double Tilde", "html": "<span class='cd'>&#9676;&#864;</span>"}, {"name": "Combining Double Inverted Breve", "html": "<span class='cd'>&#9676;&#865;</span>"}, {"name": "Combining Double RightWARDS Arrow Below", "html": "<span class='cd'>&#9676;&#866;</span>"}, {"name": "Combining Latin Small Letter A", "html": "<span class='cd'>&#9676;&#867;</span>"}, {"name": "Combining Latin Small Letter E", "html": "<span class='cd'>&#9676;&#868;</span>"}, {"name": "Combining Latin Small Letter I", "html": "<span class='cd'>&#9676;&#869;</span>"}, {"name": "Combining Latin Small Letter O", "html": "<span class='cd'>&#9676;&#870;</span>"}, {"name": "Combining Latin Small Letter U", "html": "<span class='cd'>&#9676;&#871;</span>"}, {"name": "Combining Latin Small Letter C", "html": "<span class='cd'>&#9676;&#872;</span>"}, {"name": "Combining Latin Small Letter D", "html": "<span class='cd'>&#9676;&#873;</span>"}, {"name": "Combining Latin Small Letter H", "html": "<span class='cd'>&#9676;&#874;</span>"}, {"name": "Combining Latin Small Letter M", "html": "<span class='cd'>&#9676;&#875;</span>"}, {"name": "Combining Latin Small Letter R", "html": "<span class='cd'>&#9676;&#876;</span>"}, {"name": "Combining Latin Small Letter T", "html": "<span class='cd'>&#9676;&#877;</span>"}, {"name": "Combining Latin Small Letter V", "html": "<span class='cd'>&#9676;&#878;</span>"}, {"name": "Combining Latin Small Letter X", "html": "<span class='cd'>&#9676;&#879;</span>"}, {"name": "Combining Grave Accent", "html": "<span class='cd'>&#9645;&#768;</span>"}, {"name": "Combining Acute Accent", "html": "<span class='cd'>&#9645;&#769;</span>"}, {"name": "Combining Circumflex Accent", "html": "<span class='cd'>&#9645;&#770;</span>"}, {"name": "Combining Tilde", "html": "<span class='cd'>&#9645;&#771;</span>"}, {"name": "Combining Macron", "html": "<span class='cd'>&#9645;&#772;</span>"}, {"name": "Combining Overline", "html": "<span class='cd'>&#9645;&#773;</span>"}, {"name": "Combining Breve", "html": "<span class='cd'>&#9645;&#774;</span>"}, {"name": "Combining Dot Above", "html": "<span class='cd'>&#9645;&#775;</span>"}, {"name": "Combining Diaeresis", "html": "<span class='cd'>&#9645;&#776;</span>"}, {"name": "Combining Hook Above", "html": "<span class='cd'>&#9645;&#777;</span>"}, {"name": "Combining Ring Above", "html": "<span class='cd'>&#9645;&#778;</span>"}, {"name": "Combining Double Acute Accent", "html": "<span class='cd'>&#9645;&#779;</span>"}, {"name": "Combining <PERSON>", "html": "<span class='cd'>&#9645;&#780;</span>"}, {"name": "Combining Vertical Line Above", "html": "<span class='cd'>&#9645;&#781;</span>"}, {"name": "Combining Double Vertical Line Above", "html": "<span class='cd'>&#9645;&#782;</span>"}, {"name": "Combining Double Grave Accent", "html": "<span class='cd'>&#9645;&#783;</span>"}, {"name": "Combining Candrabindu", "html": "<span class='cd'>&#9645;&#784;</span>"}, {"name": "Combining Inverted Breve", "html": "<span class='cd'>&#9645;&#785;</span>"}, {"name": "Combining Turned Comma Above", "html": "<span class='cd'>&#9645;&#786;</span>"}, {"name": "Combining Comma Above", "html": "<span class='cd'>&#9645;&#787;</span>"}, {"name": "Combining Reversed Comma Above", "html": "<span class='cd'>&#9645;&#788;</span>"}, {"name": "Combining Comma Above Right", "html": "<span class='cd'>&#9645;&#789;</span>"}, {"name": "Combining Grave Accent Below", "html": "<span class='cd'>&#9645;&#790;</span>"}, {"name": "Combining Acute Accent Below", "html": "<span class='cd'>&#9645;&#791;</span>"}, {"name": "Combining Left Tack Below", "html": "<span class='cd'>&#9645;&#792;</span>"}, {"name": "Combining Right Tack Below", "html": "<span class='cd'>&#9645;&#793;</span>"}, {"name": "Combining Left Angle Above", "html": "<span class='cd'>&#9645;&#794;</span>"}, {"name": "Combining Horn", "html": "<span class='cd'>&#9645;&#795;</span>"}, {"name": "Combining Left Half Ring Below", "html": "<span class='cd'>&#9645;&#796;</span>"}, {"name": "Combining UP Tack Below", "html": "<span class='cd'>&#9645;&#797;</span>"}, {"name": "Combining Down Tack Below", "html": "<span class='cd'>&#9645;&#798;</span>"}, {"name": "Combining Plus Sign Below", "html": "<span class='cd'>&#9645;&#799;</span>"}, {"name": "Combining Minus Sign Below", "html": "<span class='cd'>&#9645;&#800;</span>"}, {"name": "Combining Palatalized Hook Below", "html": "<span class='cd'>&#9645;&#801;</span>"}, {"name": "Combining Retroflex Hook Below", "html": "<span class='cd'>&#9645;&#802;</span>"}, {"name": "Combining Dot Below", "html": "<span class='cd'>&#9645;&#803;</span>"}, {"name": "Combining Diaeresis Below", "html": "<span class='cd'>&#9645;&#804;</span>"}, {"name": "Combining Ring Below", "html": "<span class='cd'>&#9645;&#805;</span>"}, {"name": "Combining Comma Below", "html": "<span class='cd'>&#9645;&#806;</span>"}, {"name": "Combining Cedilla", "html": "<span class='cd'>&#9645;&#807;</span>"}, {"name": "Combining Ogonek", "html": "<span class='cd'>&#9645;&#808;</span>"}, {"name": "Combining Vertical Line Below", "html": "<span class='cd'>&#9645;&#809;</span>"}, {"name": "Combining Bridge Below", "html": "<span class='cd'>&#9645;&#810;</span>"}, {"name": "Combining Inverted Double ARCH Below", "html": "<span class='cd'>&#9645;&#811;</span>"}, {"name": "Combining Caron Below", "html": "<span class='cd'>&#9645;&#812;</span>"}, {"name": "Combining Circumflex Accent Below", "html": "<span class='cd'>&#9645;&#813;</span>"}, {"name": "Combining Breve Below", "html": "<span class='cd'>&#9645;&#814;</span>"}, {"name": "Combining Inverted Breve Below", "html": "<span class='cd'>&#9645;&#815;</span>"}, {"name": "Combining Tilde Below", "html": "<span class='cd'>&#9645;&#816;</span>"}, {"name": "Combining Macron Below", "html": "<span class='cd'>&#9645;&#817;</span>"}, {"name": "Combining Low Line", "html": "<span class='cd'>&#9645;&#818;</span>"}, {"name": "Combining Double Low Line", "html": "<span class='cd'>&#9645;&#819;</span>"}, {"name": "Combining Tilde Overlay", "html": "<span class='cd'>&#9645;&#820;</span>"}, {"name": "Combining Short Stroke Overlay", "html": "<span class='cd'>&#9645;&#821;</span>"}, {"name": "Combining Long Stroke Overlay", "html": "<span class='cd'>&#9645;&#822;</span>"}, {"name": "Combining Short Solidus Overlay", "html": "<span class='cd'>&#9645;&#823;</span>"}, {"name": "Combining Long Solidus Overlay", "html": "<span class='cd'>&#9645;&#824;</span>"}, {"name": "Combining Right Half Ring Below", "html": "<span class='cd'>&#9645;&#825;</span>"}, {"name": "Combining Inverted Bridge Below", "html": "<span class='cd'>&#9645;&#826;</span>"}, {"name": "Combining Square Below", "html": "<span class='cd'>&#9645;&#827;</span>"}, {"name": "Combining Seagull Below", "html": "<span class='cd'>&#9645;&#828;</span>"}, {"name": "Combining X Above", "html": "<span class='cd'>&#9645;&#829;</span>"}, {"name": "Combining Vertical Tilde", "html": "<span class='cd'>&#9645;&#830;</span>"}, {"name": "Combining Double Overline", "html": "<span class='cd'>&#9645;&#831;</span>"}, {"name": "Combining Grave Tone <PERSON>", "html": "<span class='cd'>&#9645;&#832;</span>"}, {"name": "Combining Acute Tone Mark", "html": "<span class='cd'>&#9645;&#833;</span>"}, {"name": "Combining Greek Perispomeni", "html": "<span class='cd'>&#9645;&#834;</span>"}, {"name": "Combining Greek Ko<PERSON>is", "html": "<span class='cd'>&#9645;&#835;</span>"}, {"name": "Combining Greek Dialytika Tonos", "html": "<span class='cd'>&#9645;&#836;</span>"}, {"name": "Combining Greek Ypogegrammeni", "html": "<span class='cd'>&#9645;&#837;</span>"}, {"name": "Combining Bridge Above", "html": "<span class='cd'>&#9645;&#838;</span>"}, {"name": "Combining Equals Sign Below", "html": "<span class='cd'>&#9645;&#839;</span>"}, {"name": "Combining Double Vertical Line Below", "html": "<span class='cd'>&#9645;&#840;</span>"}, {"name": "Combining Left Angle Below", "html": "<span class='cd'>&#9645;&#841;</span>"}, {"name": "Combining Not Tilde Above", "html": "<span class='cd'>&#9645;&#842;</span>"}, {"name": "Combining Homothetic Above", "html": "<span class='cd'>&#9645;&#843;</span>"}, {"name": "Combining Almost Equal To Above", "html": "<span class='cd'>&#9645;&#844;</span>"}, {"name": "Combining Left Right Arrow Below", "html": "<span class='cd'>&#9645;&#845;</span>"}, {"name": "Combining Upwards Arrow Below", "html": "<span class='cd'>&#9645;&#846;</span>"}, {"name": "Combining Right Arrowhead Above", "html": "<span class='cd'>&#9645;&#848;</span>"}, {"name": "Combining Left Half Ring Above", "html": "<span class='cd'>&#9645;&#849;</span>"}, {"name": "Combining Fermata", "html": "<span class='cd'>&#9645;&#850;</span>"}, {"name": "Combining X Below", "html": "<span class='cd'>&#9645;&#851;</span>"}, {"name": "Combining Left Arrowhead Below", "html": "<span class='cd'>&#9645;&#852;</span>"}, {"name": "Combining Right Arrowhead Below", "html": "<span class='cd'>&#9645;&#853;</span>"}, {"name": "Combining Right Arrowhead and Up Arrowhead Below", "html": "<span class='cd'>&#9645;&#854;</span>"}, {"name": "Combining Right Half Ring Above", "html": "<span class='cd'>&#9645;&#855;</span>"}, {"name": "Combining Dot Above Right", "html": "<span class='cd'>&#9645;&#856;</span>"}, {"name": "Combining Asterisk Below", "html": "<span class='cd'>&#9645;&#857;</span>"}, {"name": "Combining Double Ring Below", "html": "<span class='cd'>&#9645;&#858;</span>"}, {"name": "Combining Zigzag Above", "html": "<span class='cd'>&#9645;&#859;</span>"}, {"name": "Combining Double Breve Below", "html": "<span class='cd'>&#9645;&#860;</span>"}, {"name": "Combining Double Breve", "html": "<span class='cd'>&#9645;&#861;</span>"}, {"name": "Combining Double Macron", "html": "<span class='cd'>&#9645;&#862;</span>"}, {"name": "Combining Double Macron Below", "html": "<span class='cd'>&#9645;&#863;</span>"}, {"name": "Combining Double Tilde", "html": "<span class='cd'>&#9645;&#864;</span>"}, {"name": "Combining Double Inverted Breve", "html": "<span class='cd'>&#9645;&#865;</span>"}, {"name": "Combining Double RightWARDS Arrow Below", "html": "<span class='cd'>&#9645;&#866;</span>"}, {"name": "Combining Latin Small Letter A", "html": "<span class='cd'>&#9645;&#867;</span>"}, {"name": "Combining Latin Small Letter E", "html": "<span class='cd'>&#9645;&#868;</span>"}, {"name": "Combining Latin Small Letter I", "html": "<span class='cd'>&#9645;&#869;</span>"}, {"name": "Combining Latin Small Letter O", "html": "<span class='cd'>&#9645;&#870;</span>"}, {"name": "Combining Latin Small Letter U", "html": "<span class='cd'>&#9645;&#871;</span>"}, {"name": "Combining Latin Small Letter C", "html": "<span class='cd'>&#9645;&#872;</span>"}, {"name": "Combining Latin Small Letter D", "html": "<span class='cd'>&#9645;&#873;</span>"}, {"name": "Combining Latin Small Letter H", "html": "<span class='cd'>&#9645;&#874;</span>"}, {"name": "Combining Latin Small Letter M", "html": "<span class='cd'>&#9645;&#875;</span>"}, {"name": "Combining Latin Small Letter R", "html": "<span class='cd'>&#9645;&#876;</span>"}, {"name": "Combining Latin Small Letter T", "html": "<span class='cd'>&#9645;&#877;</span>"}, {"name": "Combining Latin Small Letter V", "html": "<span class='cd'>&#9645;&#878;</span>"}, {"name": "Combining Latin Small Letter X", "html": "<span class='cd'>&#9645;&#879;</span>"}]}, {"name": "Font Awesome Icons", "href": "http://fontawesome.io/", "description": "Font Awesome is a large collection of general-purpose scalable vector icons.", "css": "css/font-awesome.min.css"}, {"name": "Keyboard-Layout-Editor I<PERSON>s", "href": "https://www.keyboard-layout-editor.com/", "description": "A selection of commonly used glyphs for keyboards.  All logos are trademarks of their respective owners.", "css": "css/kbd-webfont.css"}]