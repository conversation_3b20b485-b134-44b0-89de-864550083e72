[{"backcolor": "#e8e1ca", "css": "@font-face {\n  font-family: 'C64ProMono';\n  src: url(\"/fonts/C64_Pro_Mono-STYLE.eot\");\n  src: url(\"/fonts/C64_Pro_Mono-STYLE.eot?#iefix\") format('embedded-opentype'),\n       url(\"/fonts/C64_Pro_Mono-STYLE.woff\") format('woff'),\n       url(\"/fonts/C64_Pro_Mono-STYLE.ttf\") format('truetype'),\n       url(\"/fonts/C64_Pro_Mono-STYLE.svg#C64ProMono\") format('svg');\n  font-weight: normal;\n  font-style: normal;\n}\n\n.petscii {\n  display: inline-block;\n  font: normal normal normal 14px/1 C64ProMono;\n  font-size: inherit;\n  text-rendering: auto;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  transform: translate(0, 0);\n  border: solid 1px;\n}\n.keylabel9 .petscii , .keylabel10 .petscii , .keylabel11 .petscii {\n  font-size: 6px !important;\n}\n  \n.petscii-bar-bottom-left:after { content: \"\\0ee4c\"; }\n.petscii-bar-bottom-right:after { content: \"\\0ee7a\"; }\n.petscii-bar-horz-0:after { content: \"\\0ee63\"; } \n.petscii-bar-horz-1:after { content: \"\\0ee45\"; }\n.petscii-bar-horz-2:after { content: \"\\0ee44\"; }\n.petscii-bar-horz-3:after { content: \"\\0ee43\"; }\n.petscii-bar-horz-4:after { content: \"\\0ee46\"; }\n.petscii-bar-horz-5:after { content: \"\\0ee46\"; }\n.petscii-bar-horz-6:after { content: \"\\0ee52\"; }\n.petscii-bar-horz-7:after { content: \"\\0ee64\"; } \n.petscii-bar-top-left:after { content: \"\\0ee4f\"; }\n.petscii-bar-top-right:after { content: \"\\0ee50\"; }\n.petscii-bar-vert-0:after { content: \"\\0ee65\"; } \n.petscii-bar-vert-1:after { content: \"\\0ee54\"; }\n.petscii-bar-vert-2:after { content: \"\\0ee47\"; }\n.petscii-bar-vert-3:after { content: \"\\0ee42\"; }\n.petscii-bar-vert-4:after { content: \"\\0ee42\"; }\n.petscii-bar-vert-5:after { content: \"\\0ee48\"; }\n.petscii-bar-vert-6:after { content: \"\\0ee59\"; }\n.petscii-bar-vert-7:after { content: \"\\0ee67\"; } \n.petscii-block-horz-0:after { content: \"\\0ee63\"; } \n.petscii-block-horz-1:after { content: \"\\0ee77\"; } \n.petscii-block-horz-2:after { content: \"\\0ee78\"; } \n.petscii-block-horz-3:after { content: \"\\0ee62\"; } \n.petscii-block-horz-4:after { content: \"\\0ee79\"; } \n.petscii-block-horz-5:after { content: \"\\0ee6f\"; } \n.petscii-block-horz-6:after { content: \"\\0ee64\"; } \n.petscii-block-vert-0:after { content: \"\\0ee65\"; } \n.petscii-block-vert-1:after { content: \"\\0ee74\"; } \n.petscii-block-vert-2:after { content: \"\\0ee75\"; } \n.petscii-block-vert-3:after { content: \"\\0ee61\"; } \n.petscii-block-vert-4:after { content: \"\\0ee76\"; } \n.petscii-block-vert-5:after { content: \"\\0ee6a\"; } \n.petscii-block-vert-6:after { content: \"\\0ee67\"; } \n.petscii-checkerboard:after { content: \"\\0ee7f\"; } \n.petscii-circle-filled:after { content: \"\\0ee51\"; }\n.petscii-circle-open:after { content: \"\\0ee57\"; }\n.petscii-club:after { content: \"\\0ee58\"; }\n.petscii-corner-round-bottom-left:after { content: \"\\0ee49\"; }\n.petscii-corner-round-bottom-right:after { content: \"\\0ee55\"; }\n.petscii-corner-round-top-left:after { content: \"\\0ee4b\"; }\n.petscii-corner-round-top-right:after { content: \"\\0ee4a\"; }\n.petscii-corner-square-bottom-left-filled:after { content: \"\\0ee7b\"; } \n.petscii-corner-square-bottom-left:after { content: \"\\0ee6e\"; } \n.petscii-corner-square-bottom-right-filled:after { content: \"\\0ee6c\"; } \n.petscii-corner-square-bottom-right:after { content: \"\\0ee70\"; } \n.petscii-corner-square-top-left-filled:after { content: \"\\0ee7e\"; } \n.petscii-corner-square-top-left:after { content: \"\\0ee7d\"; } \n.petscii-corner-square-top-right-filled:after { content: \"\\0ee7c\"; } \n.petscii-corner-square-top-right:after { content: \"\\0ee6d\"; } \n.petscii-cross-diag:after { content: \"\\0ee56\"; }\n.petscii-cross:after { content: \"\\0ee5b\"; }\n.petscii-diag-bottom-top-filled:after { content: \"\\0ee69\"; }\n.petscii-diag-bottom-top:after { content: \"\\0ee4e\"; }\n.petscii-diag-top-bottom-filled:after { content: \"\\0ee5f\"; } \n.petscii-diag-top-bottom:after { content: \"\\0ee4d\"; }\n.petscii-diamond:after { content: \"\\0ee5a\"; }\n.petscii-halftone-bottom:after { content: \"\\0ee68\"; } \n.petscii-halftone-left:after { content: \"\\0ee5c\"; }\n.petscii-halftone:after { content: \"\\0ee66\"; }\n.petscii-heart:after { content: \"\\0ee53\"; }\n.petscii-spade:after { content: \"\\0ee41\"; }\n.petscii-tbar-down:after { content: \"\\0ee72\"; } \n.petscii-tbar-left:after { content: \"\\0ee73\"; } \n.petscii-tbar-right:after { content: \"\\0ee6b\"; } \n.petscii-tbar-up:after { content: \"\\0ee71\"; }"}, [{"x": 0.25, "c": "#413c2c", "t": "#f1ecda", "p": "SA R1", "a": 7, "f": 7}, "←", {"a": 5}, "!\n1\n\n\nBLK", {"f": 9, "f2": 7}, "\"\n2\n\n\nWHT", {"f": 5, "f2": 7}, "#\n3\n\n\nRED", "$\n4\n\n\nCYN", {"f": 7}, "%\n5\n\n\nPUR", {"f": 6, "f2": 7}, "&\n6\n\n\nGRN", {"f": 9, "f2": 7}, "’\n7\n\n\nBLU", {"f": 7}, "(\n8\n\n\nYEL", ")\n9\n\n\nRVS ON", "\n0\n\n\nRVS OFF", {"f": 9, "a": 7}, "+\n\n\n\n<i class='petscii petscii-halftone'></i> <i class='petscii petscii-cross'></i>", "-\n\n\n\n<i class='petscii petscii-halftone-left'></i> <i class='petscii petscii-bar-vert-4'></i>", "£\n\n\n\n<i class='petscii petscii-halftone-bottom'></i> <i class='petscii petscii-diag-bottom-top-filled'></i>", {"f": 5, "a": 5}, "CLR\nHOME", "INST\nDEL", {"x": 1.25, "c": "#e9bf69", "f": 9, "w": 1.5, "a": 7}, "f 1\n\n\n\nf 2"], [{"x": 0.25, "c": "#413c2c", "p": "SA R2", "f": 4, "w": 1.5, "a": 7}, "C T R L", {"f": 7}, "Q\n\n\n\n<i class='petscii petscii-tbar-right'></i> <i class='petscii petscii-circle-filled'></i>", "W\n\n\n\n<i class='petscii petscii-tbar-left'></i> <i class='petscii petscii-circle-open'></i>", "E\n\n\n\n<i class='petscii petscii-tbar-up'></i> <i class='petscii petscii-bar-horz-1'></i>", "R\n\n\n\n<i class='petscii petscii-tbar-down'></i> <i class='petscii petscii-bar-horz-6'></i>", "T\n\n\n\n<i class='petscii petscii-bar-horz-0'></i> <i class='petscii petscii-bar-vert-1'></i>", "Y\n\n\n\n<i class='petscii petscii-block-horz-1'></i> <i class='petscii petscii-bar-vert-6'></i>", "U\n\n\n\n<i class='petscii petscii-block-horz-2'></i> <i class='petscii petscii-corner-round-bottom-right'></i>", "I\n\n\n\n<i class='petscii petscii-block-horz-3'></i> <i class='petscii petscii-corner-round-bottom-left'></i>", "O\n\n\n\n<i class='petscii petscii-block-horz-4'></i> <i class='petscii petscii-bar-top-left'></i>", "P\n\n\n\n<i class='petscii petscii-block-horz-5'></i> <i class='petscii petscii-bar-top-right'></i>", "@\n\n\n\n<i class='petscii petscii-bar-horz-7'></i> <i class='petscii petscii-bar-bottom-right'></i>", {"f": 9}, "*\n\n\n\n<i class='petscii petscii-diag-top-bottom-filled'></i> <i class='petscii petscii-bar-horz-4'></i>", {"f": 7}, "↑\n\n\n\nπ", {"f": 4, "w": 1.5}, "RESTORE", {"x": 1.25, "c": "#e9bf69", "f": 9, "w": 1.5}, "f 3\n\n\n\nf 4"], [{"c": "#413c2c", "p": "SA R4", "f": 3}, "RUN STOP", "SHIFT LOCK", {"f": 7}, "A\n\n\n\n<i class='petscii petscii-corner-square-bottom-right'></i> <i class='petscii petscii-spade'></i>", "S\n\n\n\n<i class='petscii petscii-corner-square-bottom-left'></i> <i class='petscii petscii-heart'></i>", "D\n\n\n\n<i class='petscii petscii-corner-square-bottom-right-filled'></i> <i class='petscii petscii-bar-horz-2'></i>", "F\n\n\n\n<i class='petscii petscii-corner-square-bottom-left-filled'></i> <i class='petscii petscii-bar-horz-5'></i>", "G\n\n\n\n<i class='petscii petscii-bar-vert-0'></i> <i class='petscii petscii-bar-vert-2'></i>", "H\n\n\n\n<i class='petscii petscii-block-vert-1'></i> <i class='petscii petscii-bar-vert-5'></i>", "J\n\n\n\n<i class='petscii petscii-block-vert-2'></i> <i class='petscii petscii-corner-round-top-right'></i>", "K\n\n\n\n<i class='petscii petscii-block-vert-3'></i> <i class='petscii petscii-corner-round-top-left'></i>", "L\n\n\n\n<i class='petscii petscii-block-vert-4'></i> <i class='petscii petscii-bar-bottom-left'></i>", {"f": 6, "a": 5}, "[\n:", "]\n;", {"f": 7, "a": 7}, "=", {"f": 4, "w": 2}, "RETURN", {"x": 1.5, "c": "#e9bf69", "f": 9, "w": 1.5}, "f 5\n\n\n\nf6"], [{"c": "#413c2c", "p": "SA R5", "f": 7}, "C=", {"f": 4, "w": 1.5}, "S H I F T", {"f": 7}, "Z\n\n\n\n<i class='petscii petscii-corner-square-top-right'></i> <i class='petscii petscii-diamond'></i>", "X\n\n\n\n<i class='petscii petscii-corner-square-top-left'></i> <i class='petscii petscii-club'></i>", "C\n\n\n\n<i class='petscii petscii-corner-square-top-right-filled'></i> <i class='petscii petscii-bar-horz-3'></i>", "V\n\n\n\n<i class='petscii petscii-corner-square-top-left-filled'></i> <i class='petscii petscii-cross-diag'></i>", "B\n\n\n\n<i class='petscii petscii-checkerboard'></i> <i class='petscii petscii-bar-vert-3'></i>", "N\n\n\n\n<i class='petscii petscii-block-vert-5'></i> <i class='petscii petscii-diag-bottom-top'></i>", "M\n\n\n\n<i class='petscii petscii-bar-vert-7'></i> <i class='petscii petscii-diag-top-bottom'></i>", {"f": 6, "a": 5}, "<\n,", ">\n.", "?\n/", {"f": 4, "w": 1.5, "a": 7}, "S H I F T", {"f": 3, "a": 5}, "&#x021d1;\n&#x021d3;\n\n\n\n\nCRSR", "&#x021d0;\n&#x021d2;\n\n\n\n\nCRSR", {"x": 1.5, "c": "#e9bf69", "f": 9, "w": 1.5, "a": 7}, "f 7\n\n\n\nf 8"], [{"x": 2.75, "c": "#413c2c", "p": "SA SPACE", "a": 4, "f": 3, "w": 9}, ""]]