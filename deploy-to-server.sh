#!/bin/bash

# Keyboard Layout Editor - Server Deployment Script
# Usage: ./deploy-to-server.sh [server] [path] [method]
# Example: ./deploy-to-server.sh <EMAIL> /var/www/html rsync

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default values
SERVER=""
REMOTE_PATH=""
METHOD="rsync"
BUILD_DIR="dist"

# Help function
show_help() {
    echo -e "${BLUE}Keyboard Layout Editor - Deployment Script${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --server SERVER     Remote server (user@hostname)"
    echo "  -p, --path PATH         Remote path (e.g., /var/www/html)"
    echo "  -m, --method METHOD     Deployment method: rsync, scp, or ftp"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -s <EMAIL> -p /var/www/html -m rsync"
    echo "  $0 -s <EMAIL> -p /home/<USER>/public_html -m scp"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--server)
            SERVER="$2"
            shift 2
            ;;
        -p|--path)
            REMOTE_PATH="$2"
            shift 2
            ;;
        -m|--method)
            METHOD="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}❌ Build directory '$BUILD_DIR' not found!${NC}"
    echo -e "${YELLOW}💡 Run './build-production.sh' first to create the build.${NC}"
    exit 1
fi

# Interactive mode if no arguments provided
if [ -z "$SERVER" ]; then
    echo -e "${BLUE}🚀 Keyboard Layout Editor Deployment${NC}"
    echo ""
    read -p "Enter server (user@hostname): " SERVER
fi

if [ -z "$REMOTE_PATH" ]; then
    read -p "Enter remote path (e.g., /var/www/html): " REMOTE_PATH
fi

if [ -z "$METHOD" ]; then
    echo "Select deployment method:"
    echo "1) rsync (recommended)"
    echo "2) scp"
    echo "3) manual (show commands only)"
    read -p "Choose (1-3): " choice
    case $choice in
        1) METHOD="rsync" ;;
        2) METHOD="scp" ;;
        3) METHOD="manual" ;;
        *) METHOD="rsync" ;;
    esac
fi

echo ""
echo -e "${YELLOW}📋 Deployment Summary:${NC}"
echo "  Server: $SERVER"
echo "  Remote Path: $REMOTE_PATH"
echo "  Method: $METHOD"
echo "  Local Build: $BUILD_DIR"
echo ""

# Confirm deployment
read -p "Continue with deployment? (y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}❌ Deployment cancelled.${NC}"
    exit 0
fi

echo ""
echo -e "${YELLOW}🚀 Starting deployment...${NC}"

case $METHOD in
    "rsync")
        echo -e "${BLUE}📡 Using rsync to deploy files...${NC}"
        rsync -avz --progress --delete $BUILD_DIR/ $SERVER:$REMOTE_PATH/
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Rsync deployment completed successfully!${NC}"
        else
            echo -e "${RED}❌ Rsync deployment failed!${NC}"
            exit 1
        fi
        ;;
    
    "scp")
        echo -e "${BLUE}📡 Using SCP to deploy files...${NC}"
        scp -r $BUILD_DIR/* $SERVER:$REMOTE_PATH/
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ SCP deployment completed successfully!${NC}"
        else
            echo -e "${RED}❌ SCP deployment failed!${NC}"
            exit 1
        fi
        ;;
    
    "manual")
        echo -e "${BLUE}📋 Manual deployment commands:${NC}"
        echo ""
        echo "Option 1 - Using rsync:"
        echo "  rsync -avz --progress --delete $BUILD_DIR/ $SERVER:$REMOTE_PATH/"
        echo ""
        echo "Option 2 - Using SCP:"
        echo "  scp -r $BUILD_DIR/* $SERVER:$REMOTE_PATH/"
        echo ""
        echo "Option 3 - Using tar + SSH:"
        echo "  tar -czf - -C $BUILD_DIR . | ssh $SERVER 'cd $REMOTE_PATH && tar -xzf -'"
        echo ""
        echo "Option 4 - Upload compressed file:"
        echo "  scp keyboard-layout-editor-production.tar.gz $SERVER:$REMOTE_PATH/"
        echo "  ssh $SERVER 'cd $REMOTE_PATH && tar -xzf keyboard-layout-editor-production.tar.gz && rm keyboard-layout-editor-production.tar.gz'"
        exit 0
        ;;
esac

# Test deployment
echo ""
echo -e "${YELLOW}🔍 Testing deployment...${NC}"
if command -v curl &> /dev/null; then
    # Extract hostname from SERVER
    HOSTNAME=$(echo $SERVER | cut -d'@' -f2)
    echo "Testing: http://$HOSTNAME/kb.html"
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://$HOSTNAME/kb.html" || echo "000")
    if [ "$HTTP_STATUS" = "200" ]; then
        echo -e "${GREEN}✅ Website is accessible!${NC}"
    else
        echo -e "${YELLOW}⚠️  Website test returned HTTP $HTTP_STATUS${NC}"
        echo -e "${YELLOW}💡 You may need to configure your web server or check the URL.${NC}"
    fi
else
    echo -e "${YELLOW}💡 curl not found. Please test manually: http://$HOSTNAME/kb.html${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Deployment process completed!${NC}"
echo -e "${BLUE}🌐 Your Keyboard Layout Editor should be available at:${NC}"
HOSTNAME=$(echo $SERVER | cut -d'@' -f2)
echo "   http://$HOSTNAME/kb.html"
echo "   http://$HOSTNAME/ (redirects to kb.html)"
