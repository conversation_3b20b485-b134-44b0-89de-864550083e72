[{"backcolor": "#B1B1A3"}, [{"c": "#93928A", "t": "#CCCCB7", "p": "SA", "a": 7, "w": 2}, "FUNCTION", {"w": 2}, "ESCAPE", {"w": 2}, "REFRESH", {"f": 8, "w": 2}, "&#9632;", {"w": 2}, "&#9679;", {"w": 2}, "&#9650;", {"f": 3, "w": 2}, "CLEAR INPUT", {"w": 2}, "SUSPEND", {"w": 2}, "RESUME", {"w": 2}, "ABORT"], [{"w": 2}, "NETWORK", {"c": "#474644", "f": 9}, "<b>:</b>", {"a": 5, "f": 7}, "!\n1", "@\n2", "#\n3", "$\n4", "%\n5", "^\n6", "&\n7", "*\n8", "(\n9", ")\n0", {"f": 6}, "&mdash;\n&ndash;", "+\n=", "~\n'", "{\n\\", "}\n|", {"c": "#93928A", "a": 7, "f": 3, "w": 2}, "HELP"], [{"w": 2}, "LOCAL", {"w": 1.5}, "TAB", {"c": "#474644", "f": 9}, "Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P", {"a": 5, "f": 6}, "[\n(", "]\n)", {"c": "#93928A", "a": 7, "f": 3}, "BACK SPACE", {"w": 1.5}, "PAGE", {"w": 2}, "COMPLETE"], [{"w": 2}, "SELECT", {"w": 1.75}, "RUB OUT", {"c": "#474644", "f": 9}, "A", "S", "D", "F", "G", "H", "J", "K", "L", {"a": 5, "f": 6}, ":\n;", "\"\n'", {"c": "#93928A", "a": 7, "f": 3, "w": 2}, "RETURN", {"w": 1.25}, "LINE", {"w": 2}, "END"], [{"t": "#474644"}, "CAPS LOCK", {"w": 1.25}, "SYMBOL", {"w": 2}, "SHIFT", {"c": "#474644", "t": "#CCCCB7", "f": 9}, "Z", "X", "C", "V", "B", "N", "M", {"a": 5, "f": 6}, "<\n,", ">\n.", "?\n/", {"c": "#93928A", "t": "#474644", "a": 7, "f": 3, "w": 2}, "SHIFT", {"w": 1.25}, "SYMBOL", {"w": 1.25}, "REPEAT", {"w": 1.25}, "MODE LOCK"], ["HYPER", "SUPER", "META", {"w": 1.75}, "CONTROL", {"t": "#CCCCB7", "p": "SA SPACE", "w": 9}, "", {"t": "#474644", "p": "SA", "w": 1.75}, "CONTROL", "META", "SUPER", "HYPER", {"t": "#CCCCB7", "w": 1.5}, "SCROLL"]]