Open("font-src/kbd-webfont.sfd");

Select(0uE600); Import("font-src/kbd-webfont/logo-windows-7.svg",0,16);
Select(0uE601); Import("font-src/kbd-webfont/logo-windows-8.svg",0,16);
Select(0uE602); Import("font-src/kbd-webfont/logo-apple.svg",0,16);
Select(0uE603); Import("font-src/kbd-webfont/logo-apple-outline.svg",0,16);
Select(0uE604); Import("font-src/kbd-webfont/logo-vim.svg",0,16);
Select(0uE605); Import("font-src/kbd-webfont/logo-commodore.svg",0,16);
Select(0uE606); Import("font-src/kbd-webfont/logo-amiga.svg",0,16);
Select(0uE607); Import("font-src/kbd-webfont/logo-ubuntu_cof.svg",0,16);
Select(0uE608); Import("font-src/kbd-webfont/logo-ubuntu_cof-circle.svg",0,16);
Select(0uE609); Import("font-src/kbd-webfont/logo-linux-tux.svg",0,16);
Select(0uE60A); Import("font-src/kbd-webfont/logo-linux-tux-ibm.svg",0,16);
Select(0uE60B); Import("font-src/kbd-webfont/logo-linux-tux-ibm-invert.svg",0,16);
Select(0uE60C); Import("font-src/kbd-webfont/logo-atari.svg",0,16);
Select(0uE60D); Import("font-src/kbd-webfont/logo-linux-archlinux.svg",0,16);
Select(0uE60E); Import("font-src/kbd-webfont/logo-bsd-freebsd.svg",0,16);
Select(0uE60F); Import("font-src/kbd-webfont/logo-linux-centos.svg",0,16);
Select(0uE610); Import("font-src/kbd-webfont/logo-winlin-cygwin.svg",0,16);
Select(0uE611); Import("font-src/kbd-webfont/logo-linux-debian.svg",0,16);
Select(0uE612); Import("font-src/kbd-webfont/logo-linux-edubuntu.svg",0,16);
Select(0uE613); Import("font-src/kbd-webfont/logo-linux-fedora.svg",0,16);
Select(0uE614); Import("font-src/kbd-webfont/logo-linux-gentoo.svg",0,16);
Select(0uE615); Import("font-src/kbd-webfont/logo-gnu.svg",0,16);
Select(0uE616); Import("font-src/kbd-webfont/logo-linux-knoppix.svg",0,16);
Select(0uE617); Import("font-src/kbd-webfont/logo-linux-redhat.svg",0,16);
Select(0uE618); Import("font-src/kbd-webfont/logo-linux-opensuse.svg",0,16);
Select(0uE619); Import("font-src/kbd-webfont/logo-android.svg",0,16);
Select(0uE700); Import("font-src/kbd-webfont/community-hapster.svg",0,16);
Select(0uE701); Import("font-src/kbd-webfont/community-awesome.svg",0,16);
Select(0uE702); Import("font-src/kbd-webfont/community-awesome-invert.svg",0,16);
Select(0uE703); Import("font-src/kbd-webfont/copyleft.svg",0,16);
Select(0uE704); Import("font-src/kbd-webfont/batman.svg",0,16);
Select(0uE800); Import("font-src/kbd-webfont/Unicode-Escape-1.svg",0,16);
Select(0uE801); Import("font-src/kbd-webfont/Unicode-Escape-2.svg",0,16);
Select(0uE802); Import("font-src/kbd-webfont/Unicode-Pause-1.svg",0,16);
Select(0uE803); Import("font-src/kbd-webfont/Unicode-Pause-2.svg",0,16);
Select(0uE804); Import("font-src/kbd-webfont/Unicode-Break-1.svg",0,16);
Select(0uE805); Import("font-src/kbd-webfont/Unicode-Break-2.svg",0,16);
Select(0uE806); Import("font-src/kbd-webfont/Unicode-PrintScreen-1.svg",0,16);
Select(0uE807); Import("font-src/kbd-webfont/Unicode-PrintScreen-2.svg",0,16);
Select(0uE808); Import("font-src/kbd-webfont/Unicode-ClearScreen-1.svg",0,16);
Select(0uE809); Import("font-src/kbd-webfont/Unicode-ClearScreen-2.svg",0,16);
Select(0uE80A); Import("font-src/kbd-webfont/Unicode-Alternate-1.svg",0,16);
Select(0uE80B); Import("font-src/kbd-webfont/Unicode-Alternate-2.svg",0,16);
Select(0uE80C); Import("font-src/kbd-webfont/Unicode-Option-1.svg",0,16);
Select(0uE80D); Import("font-src/kbd-webfont/Unicode-Option-2.svg",0,16);
Select(0uE80E); Import("font-src/kbd-webfont/Unicode-Command-1.svg",0,16);
Select(0uE80F); Import("font-src/kbd-webfont/Unicode-Command-3.svg",0,16);
Select(0uE810); Import("font-src/kbd-webfont/Unicode-Control-1.svg",0,16);
Select(0uE811); Import("font-src/kbd-webfont/Unicode-Control-2.svg",0,16);
Select(0uE812); Import("font-src/kbd-webfont/Unicode-Control-3.svg",0,16);
Select(0uE813); Import("font-src/kbd-webfont/Unicode-Enter-1.svg",0,16);
Select(0uE814); Import("font-src/kbd-webfont/Unicode-Enter-2.svg",0,16);
Select(0uE815); Import("font-src/kbd-webfont/Return-1.svg",0,16);
Select(0uE816); Import("font-src/kbd-webfont/Return-2.svg",0,16);
Select(0uE817); Import("font-src/kbd-webfont/Return-3.svg",0,16);
Select(0uE818); Import("font-src/kbd-webfont/Return-4.svg",0,16);
Select(0uE819); Import("font-src/kbd-webfont/Unicode-BackSpace-DeleteLeft-Big.svg",0,16);
Select(0uE81A); Import("font-src/kbd-webfont/Unicode-DeleteRight-Big.svg",0,16);
Select(0uE81B); Import("font-src/kbd-webfont/Unicode-DeleteRight-Small.svg",0,16);
Select(0uE81C); Import("font-src/kbd-webfont/Unicode-BackSpace-DeleteLeft-Small.svg",0,16);
Select(0uE81D); Import("font-src/kbd-webfont/Unicode-Insert-1.svg",0,16);
Select(0uE81E); Import("font-src/kbd-webfont/Unicode-Insert-2.svg",0,16);
Select(0uE81F); Import("font-src/kbd-webfont/1-Round-Filled-1.svg",0,16);
Select(0uE820); Import("font-src/kbd-webfont/1-Round-Filled-2.svg",0,16);
Select(0uE821); Import("font-src/kbd-webfont/1-Round.svg",0,16);
Select(0uE822); Import("font-src/kbd-webfont/A-Round-Filled-Serif.svg",0,16);
Select(0uE823); Import("font-src/kbd-webfont/A-Round-Filled-SanSerif.svg",0,16);
Select(0uE824); Import("font-src/kbd-webfont/A-Square-Filled-Serif.svg",0,16);
Select(0uE825); Import("font-src/kbd-webfont/A-Square-Filled-SanSerif.svg",0,16);
Select(0uE826); Import("font-src/kbd-webfont/A-Round-SanSerif.svg",0,16);
Select(0uE827); Import("font-src/kbd-webfont/Hamburger-Menu.svg",0,16);
Select(0uE828); Import("font-src/kbd-webfont/Unicode-Hourglass-1.svg",0,16);
Select(0uE829); Import("font-src/kbd-webfont/Unicode-Hourglass-2.svg",0,16);
Select(0uE82A); Import("font-src/kbd-webfont/Unicode-Stopwatch.svg",0,16);
Select(0uE82B); Import("font-src/kbd-webfont/Unicode-Clock.svg",0,16);
Select(0uE82C); Import("font-src/kbd-webfont/Unicode-Lock-Closed-1.svg",0,16);
Select(0uE82D); Import("font-src/kbd-webfont/Unicode-Lock-Open-1.svg",0,16);
Select(0uE82E); Import("font-src/kbd-webfont/Unicode-Lock-Closed-2.svg",0,16);
Select(0uE82F); Import("font-src/kbd-webfont/Unicode-Lock-Open-2.svg",0,16);
Select(0uE830); Import("font-src/kbd-webfont/Search-1.svg",0,16);
Select(0uE831); Import("font-src/kbd-webfont/Search-2.svg",0,16);
Select(0uE832); Import("font-src/kbd-webfont/Scissors-1.svg",0,16);
Select(0uE833); Import("font-src/kbd-webfont/Scissors-2.svg",0,16);
Select(0uE834); Import("font-src/kbd-webfont/Scissors-3.svg",0,16);
Select(0uE835); Import("font-src/kbd-webfont/Undo-1.svg",0,16);
Select(0uE836); Import("font-src/kbd-webfont/Undo-2.svg",0,16);
Select(0uE837); Import("font-src/kbd-webfont/Undo-3.svg",0,16);
Select(0uE838); Import("font-src/kbd-webfont/Redo-1.svg",0,16);
Select(0uE839); Import("font-src/kbd-webfont/Unicode-Scroll-1.svg",0,16);
Select(0uE83A); Import("font-src/kbd-webfont/Unicode-Scroll-2.svg",0,16);
Select(0uE83B); Import("font-src/kbd-webfont/Unicode-Decimal-Separator-1.svg",0,16);
Select(0uE83C); Import("font-src/kbd-webfont/Unicode-Decimal-Separator-2.svg",0,16);
Select(0uE83D); Import("font-src/kbd-webfont/Unicode-Screen-Dim.svg",0,16);
Select(0uE83E); Import("font-src/kbd-webfont/Unicode-Screen-Bright.svg",0,16);
Select(0uE83F); Import("font-src/kbd-webfont/Arrows-Top-1.svg",0,16);
Select(0uE840); Import("font-src/kbd-webfont/Arrows-Top-2.svg",0,16);
Select(0uE841); Import("font-src/kbd-webfont/Arrows-Top-3.svg",0,16);
Select(0uE842); Import("font-src/kbd-webfont/Arrows-Top-4.svg",0,16);
Select(0uE843); Import("font-src/kbd-webfont/Arrows-Bottom-1.svg",0,16);
Select(0uE844); Import("font-src/kbd-webfont/Arrows-Bottom-2.svg",0,16);
Select(0uE845); Import("font-src/kbd-webfont/Arrows-Bottom-3.svg",0,16);
Select(0uE846); Import("font-src/kbd-webfont/Arrows-Bottom-4.svg",0,16);
Select(0uE847); Import("font-src/kbd-webfont/Unicode-Page-Up-1.svg",0,16);
Select(0uE848); Import("font-src/kbd-webfont/Unicode-Page-Up-2.svg",0,16);
Select(0uE849); Import("font-src/kbd-webfont/Unicode-Page-Up-3.svg",0,16);
Select(0uE84A); Import("font-src/kbd-webfont/Unicode-Page-Down-1.svg",0,16);
Select(0uE84B); Import("font-src/kbd-webfont/Unicode-Page-Down-2.svg",0,16);
Select(0uE84C); Import("font-src/kbd-webfont/Unicode-Page-Down-3.svg",0,16);
Select(0uE84D); Import("font-src/kbd-webfont/Arrows-Left.svg",0,16);
Select(0uE84E); Import("font-src/kbd-webfont/Arrows-Up.svg",0,16);
Select(0uE84F); Import("font-src/kbd-webfont/Arrows-Right.svg",0,16);
Select(0uE850); Import("font-src/kbd-webfont/Arrows-Down.svg",0,16);
Select(0uE851); Import("font-src/kbd-webfont/Arrows-Up-Left.svg",0,16);
Select(0uE852); Import("font-src/kbd-webfont/Arrows-Up-Right.svg",0,16);
Select(0uE853); Import("font-src/kbd-webfont/Line-Start.svg",0,16);
Select(0uE854); Import("font-src/kbd-webfont/Line-End.svg",0,16);
Select(0uE855); Import("font-src/kbd-webfont/Line-Start-End.svg",0,16);
Select(0uE856); Import("font-src/kbd-webfont/Tab-1.svg",0,16);
Select(0uE857); Import("font-src/kbd-webfont/Tab-2.svg",0,16);
Select(0uE858); Import("font-src/kbd-webfont/Arrows-Left-Circle-Filled.svg",0,16);
Select(0uE859); Import("font-src/kbd-webfont/Arrows-Up-Circle-Filled.svg",0,16);
Select(0uE85A); Import("font-src/kbd-webfont/Arrows-Right-Circle-Filled.svg",0,16);
Select(0uE85B); Import("font-src/kbd-webfont/Arrows-Down-Circle-Filled.svg",0,16);
Select(0uE85C); Import("font-src/kbd-webfont/Multimedia-Eject.svg",0,16);
Select(0uE85D); Import("font-src/kbd-webfont/Multimedia-Mute-1.svg",0,16);
Select(0uE85E); Import("font-src/kbd-webfont/Multimedia-Mute-2.svg",0,16);
Select(0uE85F); Import("font-src/kbd-webfont/Multimedia-Mute-3.svg",0,16);
Select(0uE860); Import("font-src/kbd-webfont/Multimedia-Mute-4.svg",0,16);
Select(0uE861); Import("font-src/kbd-webfont/Multimedia-Volume-Down-1.svg",0,16);
Select(0uE862); Import("font-src/kbd-webfont/Multimedia-Volume-Down-2.svg",0,16);
Select(0uE863); Import("font-src/kbd-webfont/Multimedia-Volume-Up-1.svg",0,16);
Select(0uE864); Import("font-src/kbd-webfont/Multimedia-Volume-Up-2.svg",0,16);
Select(0uE865); Import("font-src/kbd-webfont/Multimedia-FastForwar.svg",0,16);
Select(0uE866); Import("font-src/kbd-webfont/Multimedia-Rewind.svg",0,16);
Select(0uE867); Import("font-src/kbd-webfont/Multimedia-FastForward-End.svg",0,16);
Select(0uE868); Import("font-src/kbd-webfont/Multimedia-Rewind-Start.svg",0,16);
Select(0uE869); Import("font-src/kbd-webfont/Multimedia-Play-Pause.svg",0,16);
Select(0uE86A); Import("font-src/kbd-webfont/Multimedia-Back.svg",0,16);
Select(0uE86B); Import("font-src/kbd-webfont/Multimedia-Play.svg",0,16);
Select(0uE86C); Import("font-src/kbd-webfont/Multimedia-Up.svg",0,16);
Select(0uE86D); Import("font-src/kbd-webfont/Multimedia-Down.svg",0,16);
Select(0uE86E); Import("font-src/kbd-webfont/Multimedia-Pause.svg",0,16);
Select(0uE86F); Import("font-src/kbd-webfont/Multimedia-Stop.svg",0,16);
Select(0uE870); Import("font-src/kbd-webfont/Multimedia-Record.svg",0,16);
Select(0uE8A0); Import("font-src/kbd-webfont/Symbol-Ankh.svg",0,16);
Select(0uE8A1); Import("font-src/kbd-webfont/Symbol-Peace.svg",0,16);
Select(0uE8A2); Import("font-src/kbd-webfont/Symbol-Alien.svg",0,16);
Select(0uE8A3); Import("font-src/kbd-webfont/Symbol-Skull-Bones-1.svg",0,16);
Select(0uE8A4); Import("font-src/kbd-webfont/Symbol-Skull-Bones-2.svg",0,16);
Select(0uE8A5); Import("font-src/kbd-webfont/Symbol-Keyboard.svg",0,16);
Select(0uE8A6); Import("font-src/kbd-webfont/Symbol-YinYang.svg",0,16);

Generate($2);
