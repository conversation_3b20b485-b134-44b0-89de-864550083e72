Contributors
------------
Special thanks to the following contributors:
* [gcolic](https://github.com/gcollic), for providing WASD color presets
* [D1SC0tech](https://github.com/D1SC0tech), for initial DSA homing/deep-dish 
  support
* [jasonm23](https://github.com/jasonm23), for initial Custom Styles 
  implementation
* [iandoug](http://iandoug.com/?p=64), for first versions of: Move/Size/Angle stepsize; case background textures & 
  related options; default switch options.
* [<PERSON>](https://github.com/datatravelandexperiments) for the Colors in Keyboard patch

The following users have contributed some of the sample keyboard layouts & 
presets:
* [domgetter](https://github.com/domgetter)
* [rswiernik](https://github.com/rswiernik)
* [BlueNalgene](https://github.com/BlueNalgene)
* [alerque](https://github.com/alerque)
* [jasonm23](https://github.com/jasonm23)
* [iandoug](http://iandoug.com/?p=64)
* [mikebell](https://github.com/mikebell)
* [alistaircom](https://github.com/alistaircom)

Third-Party Software
--------------------
The following third-party software packages were used in the creation of 
keyboard-layout-editor.com:
* [AngularJS](https://angularjs.org/)
* [AngularUI](https://angular-ui.github.io/)
* [ng-file-upload](https://github.com/danialfarid/ng-file-upload)
* [angular-dragdrop](https://github.com/angular-dragdrop/angular-dragdrop)
* [angular-bootstrap-colorpicker](https://github.com/buberdds/angular-bootstrap-colorpicker)
* [Bootstrap](http://getbootstrap.com/)
* [JQuery](https://jquery.com/)
* [ACE Editor](http://ace.c9.io/#nav=about)
* [colors.js](https://gist.github.com/mikelikespie/641528) (quick RGB to LAB conversions)
* [marked](https://github.com/chjj/marked) (Markdown parser)
* [crypto-js](https://code.google.com/p/crypto-js/) 
* [FileSaver.js](https://github.com/eligrey/FileSaver.js)
* [URLON](https://github.com/vjeux/URLON) (URL Object Notation)
* [Jison](http://zaach.github.io/jison/) (JavaScript parser generator)
* [Hint.css](http://kushagragour.in/lab/hint/) (CSS-only tooltips)
* [doT.js](http://olado.github.io/doT/) (fast micro-templating)
* [html2canvas](https://github.com/niklasvh/html2canvas)
* [Font Awesome](http://fortawesome.github.io/Font-Awesome/)
* [C64 TrueType Font](http://style64.org/c64-truetype) font (by 
  [Style64.org](https://www.style64.org))
* [GitHub API](https://developer.github.com/v3/)
* [Gatekeeper](https://github.com/prose/gatekeeper) (server-side, for GitHub OAuth logins)

Backgrounds Textures
--------------------
There is an increasing trend towards using non-plastic materials to make 
custom keyboards.  We provide selection of background textures to aid in
designing & visualizing such keyboards.

The textures were sourced from various sites around the web and are believed 
to all be free to use.  In some cases, the source images were scaled, rotated, 
or cropped.  If you feel your copyright is being infringed, please advise and 
the offending material will be removed.

Sources:

* Many of the materials were sourced from [bgfons.com](http://bgfons.com):
  * [Aluminium](http://bgfons.com/img/materials/aluminum)
  * [Carbon fibres](http://bgfons.com/img/materials/carbon)
  * [Titanium](http://bgfons.com/img/materials/titanium)
  * [Iron/Steel](http://bgfons.com/img/materials/iron)
  * [Marble](http://bgfons.com/img/materials/marble)
  * [Leather](http://bgfons.com/img/materials/leather)
* The 'Plastic' backgrounds were created by selecting a small, defect-free 
  region of photographs of [Signature Plastics](http://www.keycapsdirect.com/) 
  colour swatches and then making them into seamless tiles in Gimp.
  * Note that Signature Plastics does not manufacture actual keyboard cases.
* Two of the 'Leather' textures ('Black' and 'Brown') are from
  [Free PSD Files](http://freepsdfiles.net/backgrounds/4-free-leather-textures).
* 'Red Mahogany' is by DeviantArt user [SweetSoulSister](http://sweetsoulsister.deviantart.com/art/Red-Mahogany-Wood-Texture-146083467)
* Most of the 'Wood' textures and some of the metallic textures are from 
  [CadNav](http://www.cadnav.com/), who have the following license:

  ````
  INFO: Please read and comply with the terms and conditions, otherwise please 
  don't use this model
  
  1. We don't accept any claims regarding quality of 3D model or any standards 
  conformity.
  2. We will not participate in any technology or copyright issues.
  3. This file (models or textures) may be used in any commercial way only as a 
  part of artwork or project. Single reselling or redistribution of this model 
  is prohibited.  
  4. This file (models or textures) may be freely modificated or elaborated. 
  5. If you use this file (models or textures) in your project or website, 
  please indicate the source from cadnav.com.
  ````
